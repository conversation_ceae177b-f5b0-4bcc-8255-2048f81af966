version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: bridge-postgres
    restart: unless-stopped
    environment:
      POSTGRES_USER: bridge_user
      POSTGRES_PASSWORD: bridge_password
      POSTGRES_DB: bridge_db
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - bridge-network

  # Indexer 服务
  indexer:
    image: coinflow/bridge-indexer-dev:latest
    container_name: bridge-indexer
    restart: unless-stopped
    depends_on:
      - postgres
    environment:
      - NODE_ENV=production
      - DATABASE_URL=******************************************************/bridge_db
    volumes:
      - ./indexer/.env:/app/.env:ro
    networks:
      - bridge-network

  # Web 前端服务
  web:
    image: coinflow/bridge-web-dev:latest
    container_name: bridge-web
    restart: unless-stopped
    ports:
      - "5173:80"
    volumes:
      - ./frontend/.env:/app/.env:ro
    networks:
      - bridge-network

networks:
  bridge-network:
    driver: bridge

volumes:
  postgres_data:
  redis_data: