#!/bin/bash

# 添加白名单脚本
# 使用方法: ./add_whitelist.sh <bridge_address> <token_address> <network>
# 示例: ./add_whitelist.sh 0x1bba0023e66F88D3Ee8A0460515253188B6483Ea 0x74D2269BA7C2cD2874F94a80e7C7e74a1576230B brt_testnet

set -e  # 遇到错误时退出

# 检查是否提供了足够的参数
if [ $# -lt 3 ]; then
    echo "使用方法: $0 <bridge_address> <token_address> <network>"
    echo "参数说明:"
    echo "  bridge_address - Bridge合约地址"
    echo "  token_address  - Token合约地址"
    echo "  network        - 网络名称 (brt_testnet, bsc_testnet, localhost)"
    echo ""
    echo "示例:"
    echo "  $0 0x1bba0023e66F88D3Ee8A0460515253188B6483Ea 0x74D2269BA7C2cD2874F94a80e7C7e74a1576230B brt_testnet"
    exit 1
fi

BRIDGE_ADDRESS=$1
TOKEN_ADDRESS=$2
NETWORK=$3

# 检查 .env 文件是否存在
if [ ! -f .env ]; then
    echo "错误: .env 文件不存在"
    exit 1
fi

# 加载环境变量
source .env

# 检查私钥是否配置
if [ "$PRIVATE_KEY" = "your_private_key_here" ] || [ -z "$PRIVATE_KEY" ]; then
    echo "错误: 请在 .env 文件中配置您的私钥"
    exit 1
fi

echo "=== 添加白名单脚本 ==="
echo "Bridge合约地址: $BRIDGE_ADDRESS"
echo "Token地址: $TOKEN_ADDRESS"
echo "网络: $NETWORK"
echo ""

# 设置环境变量
export BRIDGE_ADDRESS=$BRIDGE_ADDRESS
export TOKEN_ADDRESS=$TOKEN_ADDRESS

# 运行脚本
echo "正在执行添加白名单操作..."
forge script script/AddWhitelist.s.sol:AddWhitelistScript \
    --rpc-url $NETWORK \
    --private-key $PRIVATE_KEY \
    --broadcast \
    -vvvv

echo ""
echo "添加白名单完成！"
echo "可以通过调用 whitelistedTokens($TOKEN_ADDRESS) 来验证token是否已被添加到白名单" 