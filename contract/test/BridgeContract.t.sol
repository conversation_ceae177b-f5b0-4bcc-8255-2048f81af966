// SPDX-License-Identifier: MIT

pragma solidity ^0.8.0;

import {Test, console} from "forge-std/Test.sol";
import {BridgeContract} from "../src/BridgeContract.sol";
import {EtherERC20} from "../src/EtherERC20.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";

contract BridgeContractTest is Test {
    BridgeContract public bridge;
    EtherERC20 public token;

    address public owner = address(1);
    address public user = address(2);

    uint256 public constant INITIAL_SUPPLY = 1_000_000 ether;
    uint256 public constant BRIDGE_AMOUNT = 100 ether;
    uint256 public constant MAX_AMOUNT = 500 ether;
    uint256 public constant MIN_AMOUNT = 10 ether;

    // 测试用的交易哈希 - 使用真实的交易哈希格式
    bytes32 public constant TEST_TX_HASH = keccak256("test_transaction_hash_1");
    bytes32 public constant TEST_TX_HASH_2 = keccak256("test_transaction_hash_2");
    bytes32 public constant TEST_TX_HASH_3 = keccak256("test_transaction_hash_3");

    // 事件定义
    event Bridge(IERC20 indexed token, uint256 amount, address indexed sender);
    event Redeem(IERC20 indexed token, address indexed to, uint256 amount, bytes32 indexed txHash);
    event TokenWhitelisted(IERC20 indexed token, bool status);
    event MaxAmountUpdated(IERC20 indexed token, uint256 maxAmount);
    event MinAmountUpdated(IERC20 indexed token, uint256 minAmount);

    function setUp() public {
        vm.startPrank(owner);
        bridge = new BridgeContract();
        token = new EtherERC20();

        // 将代币加入白名单
        bridge.setTokenWhitelist(IERC20(address(token)), true);

        // 设置最大和最小金额
        bridge.setMaxAmount(IERC20(address(token)), MAX_AMOUNT);
        bridge.setMinAmount(IERC20(address(token)), MIN_AMOUNT);

        // 向用户转账一些代币
        token.transfer(user, INITIAL_SUPPLY / 2);
        vm.stopPrank();
    }

    /*//////////////////////////////////////////////////////////////
                            桥接功能测试
    //////////////////////////////////////////////////////////////*/

    function testBridgeTokens() public {
        vm.startPrank(user);

        // 为桥接合约授权代币
        token.approve(address(bridge), BRIDGE_AMOUNT);

        // 检查初始余额
        uint256 userBalanceBefore = token.balanceOf(user);
        uint256 bridgeBalanceBefore = token.balanceOf(address(bridge));

        // 期望Bridge事件被触发
        vm.expectEmit(true, true, false, true);
        emit Bridge(IERC20(address(token)), BRIDGE_AMOUNT, user);

        // 桥接代币
        bridge.bridge(IERC20(address(token)), BRIDGE_AMOUNT);

        // 检查最终余额
        uint256 userBalanceAfter = token.balanceOf(user);
        uint256 bridgeBalanceAfter = token.balanceOf(address(bridge));

        assertEq(userBalanceAfter, userBalanceBefore - BRIDGE_AMOUNT, "User balance should decrease");
        assertEq(bridgeBalanceAfter, bridgeBalanceBefore + BRIDGE_AMOUNT, "Bridge balance should increase");

        vm.stopPrank();
    }

    function testBridgeMultipleAmounts() public {
        vm.startPrank(user);

        // 测试多次桥接不同金额
        uint256[] memory amounts = new uint256[](3);
        amounts[0] = MIN_AMOUNT;
        amounts[1] = BRIDGE_AMOUNT;
        amounts[2] = MAX_AMOUNT;

        uint256 totalAmount = amounts[0] + amounts[1] + amounts[2];
        token.approve(address(bridge), totalAmount);

        uint256 initialBalance = token.balanceOf(user);

        for (uint256 i = 0; i < amounts.length; i++) {
            bridge.bridge(IERC20(address(token)), amounts[i]);
        }

        assertEq(token.balanceOf(user), initialBalance - totalAmount, "Total user balance should decrease correctly");
        assertEq(token.balanceOf(address(bridge)), totalAmount, "Total bridge balance should be correct");

        vm.stopPrank();
    }

    /*//////////////////////////////////////////////////////////////
                            赎回功能测试
    //////////////////////////////////////////////////////////////*/

    function testRedeemTokens() public {
        // 首先桥接一些代币
        vm.startPrank(user);
        token.approve(address(bridge), BRIDGE_AMOUNT);
        bridge.bridge(IERC20(address(token)), BRIDGE_AMOUNT);
        vm.stopPrank();

        // 作为owner赎回代币
        vm.startPrank(owner);

        // 检查初始余额
        uint256 recipientBalanceBefore = token.balanceOf(user);
        uint256 bridgeBalanceBefore = token.balanceOf(address(bridge));

        // 期望Redeem事件被触发
        vm.expectEmit(true, true, true, true);
        emit Redeem(IERC20(address(token)), user, BRIDGE_AMOUNT, TEST_TX_HASH);

        // 赎回代币
        bridge.redeem(IERC20(address(token)), user, BRIDGE_AMOUNT, TEST_TX_HASH);

        // 检查最终余额
        uint256 recipientBalanceAfter = token.balanceOf(user);
        uint256 bridgeBalanceAfter = token.balanceOf(address(bridge));

        assertEq(recipientBalanceAfter, recipientBalanceBefore + BRIDGE_AMOUNT, "Recipient balance should increase");
        assertEq(bridgeBalanceAfter, bridgeBalanceBefore - BRIDGE_AMOUNT, "Bridge balance should decrease");
        assertTrue(bridge.isTransactionProcessed(TEST_TX_HASH), "Transaction should be marked as processed");

        vm.stopPrank();
    }

    /*//////////////////////////////////////////////////////////////
                        交易哈希去重测试
    //////////////////////////////////////////////////////////////*/

    function testCannotRedeemSameTransactionTwice() public {
        // 首先桥接足够的代币
        vm.startPrank(user);
        token.approve(address(bridge), BRIDGE_AMOUNT * 2);
        bridge.bridge(IERC20(address(token)), BRIDGE_AMOUNT * 2);
        vm.stopPrank();

        vm.startPrank(owner);

        // 第一次赎回成功
        bridge.redeem(IERC20(address(token)), user, BRIDGE_AMOUNT, TEST_TX_HASH);
        assertTrue(bridge.isTransactionProcessed(TEST_TX_HASH), "First transaction should be processed");

        // 尝试使用相同的交易哈希再次赎回应该失败
        vm.expectRevert(BridgeContract.BridgeContract__Transaction_Already_Processed.selector);
        bridge.redeem(IERC20(address(token)), user, BRIDGE_AMOUNT, TEST_TX_HASH);

        vm.stopPrank();
    }

    function testCanRedeemDifferentTransactions() public {
        // 首先桥接足够的代币
        vm.startPrank(user);
        token.approve(address(bridge), BRIDGE_AMOUNT * 3);
        bridge.bridge(IERC20(address(token)), BRIDGE_AMOUNT * 3);
        vm.stopPrank();

        vm.startPrank(owner);

        // 使用第一个交易哈希赎回
        bridge.redeem(IERC20(address(token)), user, BRIDGE_AMOUNT, TEST_TX_HASH);
        assertTrue(bridge.isTransactionProcessed(TEST_TX_HASH), "First transaction should be processed");

        // 使用第二个交易哈希赎回应该成功
        bridge.redeem(IERC20(address(token)), user, BRIDGE_AMOUNT, TEST_TX_HASH_2);
        assertTrue(bridge.isTransactionProcessed(TEST_TX_HASH_2), "Second transaction should be processed");

        // 使用第三个交易哈希赎回应该成功
        bridge.redeem(IERC20(address(token)), user, BRIDGE_AMOUNT, TEST_TX_HASH_3);
        assertTrue(bridge.isTransactionProcessed(TEST_TX_HASH_3), "Third transaction should be processed");

        vm.stopPrank();
    }

    function testIsTransactionProcessed() public {
        // 初始状态下交易未被处理
        assertFalse(bridge.isTransactionProcessed(TEST_TX_HASH), "Transaction should initially be unprocessed");

        // 首先桥接一些代币
        vm.startPrank(user);
        token.approve(address(bridge), BRIDGE_AMOUNT);
        bridge.bridge(IERC20(address(token)), BRIDGE_AMOUNT);
        vm.stopPrank();

        // 交易仍然未被处理（只有赎回后才会被标记）
        assertFalse(
            bridge.isTransactionProcessed(TEST_TX_HASH), "Transaction should still be unprocessed before redeem"
        );

        // 赎回后交易应该被标记为已处理
        vm.startPrank(owner);
        bridge.redeem(IERC20(address(token)), user, BRIDGE_AMOUNT, TEST_TX_HASH);
        assertTrue(
            bridge.isTransactionProcessed(TEST_TX_HASH), "Transaction should be marked as processed after redeem"
        );
        vm.stopPrank();
    }

    /*//////////////////////////////////////////////////////////////
                        白名单管理测试
    //////////////////////////////////////////////////////////////*/

    function testWhitelistToken() public {
        // 创建一个新代币
        vm.startPrank(owner);
        EtherERC20 newToken = new EtherERC20();

        // 检查初始状态
        assertFalse(
            bridge.whitelistedTokens(IERC20(address(newToken))), "New token should not be whitelisted initially"
        );

        // 期望TokenWhitelisted事件被触发
        vm.expectEmit(true, false, false, true);
        emit TokenWhitelisted(IERC20(address(newToken)), true);

        // 将代币加入白名单
        bridge.setTokenWhitelist(IERC20(address(newToken)), true);

        // 检查代币是否已加入白名单
        assertTrue(bridge.whitelistedTokens(IERC20(address(newToken))), "Token should be whitelisted");

        // 期望TokenWhitelisted事件被触发（移除）
        vm.expectEmit(true, false, false, true);
        emit TokenWhitelisted(IERC20(address(newToken)), false);

        // 将代币从白名单移除
        bridge.setTokenWhitelist(IERC20(address(newToken)), false);

        // 检查代币是否不在白名单中
        assertFalse(bridge.whitelistedTokens(IERC20(address(newToken))), "Token should not be whitelisted");

        vm.stopPrank();
    }

    /*//////////////////////////////////////////////////////////////
                        金额限制测试
    //////////////////////////////////////////////////////////////*/

    function testSetMaxAmount() public {
        uint256 newMaxAmount = 1000 ether;

        vm.startPrank(owner);

        // 期望MaxAmountUpdated事件被触发
        vm.expectEmit(true, false, false, true);
        emit MaxAmountUpdated(IERC20(address(token)), newMaxAmount);

        // 设置最大金额
        bridge.setMaxAmount(IERC20(address(token)), newMaxAmount);

        // 检查最大金额
        assertEq(bridge.maxAmounts(IERC20(address(token))), newMaxAmount, "Max amount should be updated");

        vm.stopPrank();
    }

    function testSetMinAmount() public {
        uint256 newMinAmount = 5 ether;

        vm.startPrank(owner);

        // 期望MinAmountUpdated事件被触发
        vm.expectEmit(true, false, false, true);
        emit MinAmountUpdated(IERC20(address(token)), newMinAmount);

        // 设置最小金额
        bridge.setMinAmount(IERC20(address(token)), newMinAmount);

        // 检查最小金额
        assertEq(bridge.minAmounts(IERC20(address(token))), newMinAmount, "Min amount should be updated");

        vm.stopPrank();
    }

    /*//////////////////////////////////////////////////////////////
                        代币恢复测试
    //////////////////////////////////////////////////////////////*/

    function testRecoverTokens() public {
        // 首先桥接一些代币
        vm.startPrank(user);
        token.approve(address(bridge), BRIDGE_AMOUNT);
        bridge.bridge(IERC20(address(token)), BRIDGE_AMOUNT);
        vm.stopPrank();

        vm.startPrank(owner);

        // 检查初始余额
        uint256 ownerBalanceBefore = token.balanceOf(owner);
        uint256 bridgeBalanceBefore = token.balanceOf(address(bridge));

        // 恢复代币
        bridge.recoverTokens(IERC20(address(token)), BRIDGE_AMOUNT);

        // 检查最终余额
        uint256 ownerBalanceAfter = token.balanceOf(owner);
        uint256 bridgeBalanceAfter = token.balanceOf(address(bridge));

        assertEq(ownerBalanceAfter, ownerBalanceBefore + BRIDGE_AMOUNT, "Owner balance should increase");
        assertEq(bridgeBalanceAfter, bridgeBalanceBefore - BRIDGE_AMOUNT, "Bridge balance should decrease");

        vm.stopPrank();
    }

    /*//////////////////////////////////////////////////////////////
                            错误案例测试
    //////////////////////////////////////////////////////////////*/

    function testCannotBridgeNonWhitelistedToken() public {
        // 创建一个未加入白名单的代币
        vm.startPrank(owner);
        EtherERC20 nonWhitelistedToken = new EtherERC20();
        nonWhitelistedToken.transfer(user, INITIAL_SUPPLY / 2);
        vm.stopPrank();

        vm.startPrank(user);
        nonWhitelistedToken.approve(address(bridge), BRIDGE_AMOUNT);

        // 期望回滚
        vm.expectRevert(BridgeContract.BridgeContract__Token_Not_Whitelisted.selector);
        bridge.bridge(IERC20(address(nonWhitelistedToken)), BRIDGE_AMOUNT);

        vm.stopPrank();
    }

    function testCannotBridgeAmountTooLarge() public {
        vm.startPrank(user);

        uint256 tooLargeAmount = MAX_AMOUNT + 1 ether;
        // 为桥接合约授权代币
        token.approve(address(bridge), tooLargeAmount);

        // 期望回滚
        vm.expectRevert(BridgeContract.BridgeContract__Amount_Too_Large.selector);
        bridge.bridge(IERC20(address(token)), tooLargeAmount);

        vm.stopPrank();
    }

    function testCannotBridgeAmountTooSmall() public {
        vm.startPrank(user);

        uint256 tooSmallAmount = MIN_AMOUNT - 1;
        // 为桥接合约授权代币
        token.approve(address(bridge), tooSmallAmount);

        // 期望回滚
        vm.expectRevert(BridgeContract.BridgeContract__Amount_Too_Small.selector);
        bridge.bridge(IERC20(address(token)), tooSmallAmount);

        vm.stopPrank();
    }

    function testCannotBridgeWithInsufficientAllowance() public {
        vm.startPrank(user);

        // 授权少于所需的代币
        token.approve(address(bridge), BRIDGE_AMOUNT - 1);

        // 期望回滚
        vm.expectRevert(BridgeContract.BridgeContract__Insufficient_Allowance.selector);
        bridge.bridge(IERC20(address(token)), BRIDGE_AMOUNT);

        vm.stopPrank();
    }

    function testCannotBridgeWithZeroAmount() public {
        vm.startPrank(user);

        // 授权代币
        token.approve(address(bridge), BRIDGE_AMOUNT);

        // 期望回滚（金额太小）
        vm.expectRevert(BridgeContract.BridgeContract__Amount_Too_Small.selector);
        bridge.bridge(IERC20(address(token)), 0);

        vm.stopPrank();
    }

    function testCannotRedeemNonWhitelistedToken() public {
        // 创建一个未加入白名单的代币
        vm.startPrank(owner);
        EtherERC20 nonWhitelistedToken = new EtherERC20();

        // 期望回滚
        vm.expectRevert(BridgeContract.BridgeContract__Token_Not_Whitelisted.selector);
        bridge.redeem(IERC20(address(nonWhitelistedToken)), user, BRIDGE_AMOUNT, TEST_TX_HASH);

        vm.stopPrank();
    }

    function testCannotRedeemWithZeroHash() public {
        // 首先桥接一些代币
        vm.startPrank(user);
        token.approve(address(bridge), BRIDGE_AMOUNT);
        bridge.bridge(IERC20(address(token)), BRIDGE_AMOUNT);
        vm.stopPrank();

        vm.startPrank(owner);

        // 使用零哈希尝试赎回（应该可以工作，因为零哈希也是有效的哈希）
        bridge.redeem(IERC20(address(token)), user, BRIDGE_AMOUNT, bytes32(0));
        assertTrue(bridge.isTransactionProcessed(bytes32(0)), "Zero hash should be processed");

        vm.stopPrank();
    }

    /*//////////////////////////////////////////////////////////////
                        转账失败测试
    //////////////////////////////////////////////////////////////*/

    function test_RevertWhen_TransferFailsInRedeem() public {
        // 首先桥接一些代币
        vm.startPrank(user);
        token.approve(address(bridge), BRIDGE_AMOUNT);
        bridge.bridge(IERC20(address(token)), BRIDGE_AMOUNT);
        vm.stopPrank();

        // 设置代币转账失败
        vm.startPrank(owner);
        token.setFailTransfers(true);

        // 期望在赎回时回滚
        vm.expectRevert(BridgeContract.BridgeContract__Transaction_Failed.selector);
        bridge.redeem(IERC20(address(token)), user, BRIDGE_AMOUNT, TEST_TX_HASH);

        // 验证交易未被标记为已处理
        assertFalse(bridge.isTransactionProcessed(TEST_TX_HASH), "Failed transaction should not be marked as processed");

        // 重置代币不失败转账
        token.setFailTransfers(false);
        vm.stopPrank();
    }

    function test_RevertWhen_TransferFailsInRecoverTokens() public {
        // 首先桥接一些代币
        vm.startPrank(user);
        token.approve(address(bridge), BRIDGE_AMOUNT);
        bridge.bridge(IERC20(address(token)), BRIDGE_AMOUNT);
        vm.stopPrank();

        // 设置代币转账失败
        vm.startPrank(owner);
        token.setFailTransfers(true);

        // 期望在恢复代币时回滚
        vm.expectRevert(BridgeContract.BridgeContract__Transaction_Failed.selector);
        bridge.recoverTokens(IERC20(address(token)), BRIDGE_AMOUNT);

        // 重置代币不失败转账
        token.setFailTransfers(false);
        vm.stopPrank();
    }

    /*//////////////////////////////////////////////////////////////
                        权限控制测试
    //////////////////////////////////////////////////////////////*/

    function testOnlyOwnerCanRedeem() public {
        vm.startPrank(user);

        // 期望回滚 - 只有owner可以赎回
        vm.expectRevert();
        bridge.redeem(IERC20(address(token)), user, BRIDGE_AMOUNT, TEST_TX_HASH);

        vm.stopPrank();
    }

    function testOnlyOwnerCanSetWhitelist() public {
        vm.startPrank(user);

        // 期望回滚 - 只有owner可以设置白名单
        vm.expectRevert();
        bridge.setTokenWhitelist(IERC20(address(token)), false);

        vm.stopPrank();
    }

    function testOnlyOwnerCanSetMaxAmount() public {
        vm.startPrank(user);

        // 期望回滚 - 只有owner可以设置最大金额
        vm.expectRevert();
        bridge.setMaxAmount(IERC20(address(token)), 1000 ether);

        vm.stopPrank();
    }

    function testOnlyOwnerCanSetMinAmount() public {
        vm.startPrank(user);

        // 期望回滚 - 只有owner可以设置最小金额
        vm.expectRevert();
        bridge.setMinAmount(IERC20(address(token)), 5 ether);

        vm.stopPrank();
    }

    function testOnlyOwnerCanRecoverTokens() public {
        vm.startPrank(user);

        // 期望回滚 - 只有owner可以恢复代币
        vm.expectRevert();
        bridge.recoverTokens(IERC20(address(token)), BRIDGE_AMOUNT);

        vm.stopPrank();
    }

    /*//////////////////////////////////////////////////////////////
                        边界条件测试
    //////////////////////////////////////////////////////////////*/

    function testBridgeExactMinAmount() public {
        vm.startPrank(user);

        token.approve(address(bridge), MIN_AMOUNT);

        // 应该可以桥接最小金额
        bridge.bridge(IERC20(address(token)), MIN_AMOUNT);

        assertEq(token.balanceOf(address(bridge)), MIN_AMOUNT, "Should bridge exact min amount");

        vm.stopPrank();
    }

    function testBridgeExactMaxAmount() public {
        vm.startPrank(user);

        token.approve(address(bridge), MAX_AMOUNT);

        // 应该可以桥接最大金额
        bridge.bridge(IERC20(address(token)), MAX_AMOUNT);

        assertEq(token.balanceOf(address(bridge)), MAX_AMOUNT, "Should bridge exact max amount");

        vm.stopPrank();
    }

    function testSetMaxAmountToZero() public {
        vm.startPrank(owner);

        // 设置最大金额为0（表示无限制）
        bridge.setMaxAmount(IERC20(address(token)), 0);

        assertEq(bridge.maxAmounts(IERC20(address(token))), 0, "Max amount should be zero");

        vm.stopPrank();

        // 现在应该可以桥接超过原来最大金额的代币
        vm.startPrank(user);
        uint256 largeAmount = MAX_AMOUNT * 2;
        token.approve(address(bridge), largeAmount);
        bridge.bridge(IERC20(address(token)), largeAmount);

        assertEq(token.balanceOf(address(bridge)), largeAmount, "Should bridge large amount when max is zero");

        vm.stopPrank();
    }

    function testSetMinAmountToZero() public {
        vm.startPrank(owner);

        // 设置最小金额为0（表示无限制）
        bridge.setMinAmount(IERC20(address(token)), 0);

        assertEq(bridge.minAmounts(IERC20(address(token))), 0, "Min amount should be zero");

        vm.stopPrank();

        // 现在应该可以桥接任意小金额的代币
        vm.startPrank(user);
        uint256 smallAmount = 1 wei;
        token.approve(address(bridge), smallAmount);
        bridge.bridge(IERC20(address(token)), smallAmount);

        assertEq(token.balanceOf(address(bridge)), smallAmount, "Should bridge small amount when min is zero");

        vm.stopPrank();
    }
}
