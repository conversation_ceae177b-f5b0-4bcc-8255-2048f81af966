# 跨链桥接合约部署指南

## 配置环境变量

1. **复制配置文件**
```bash
cp .env.example .env
```

2. **编辑 .env 文件，配置您的 RPC URL 和私钥**
```bash
# 测试网 RPC URLs
BSC_TESTNET_RPC_URL=https://data-seed-prebsc-1-s1.binance.org:8545/
POLYGON_MUMBAI_RPC_URL=https://rpc-mumbai.maticvigil.com/
SEPOLIA_RPC_URL=https://sepolia.infura.io/v3/YOUR_INFURA_KEY

# 您的私钥（不要提交到代码仓库！）
PRIVATE_KEY=0x你的私钥

# 可选：用于合约验证的 API Keys
ETHERSCAN_API_KEY=your_etherscan_api_key
BSCSCAN_API_KEY=your_bscscan_api_key
POLYGONSCAN_API_KEY=your_polygonscan_api_key
```

## 部署方法

### 方法一：使用部署脚本（推荐）

```bash
# 部署到 Sepolia 测试网
./deploy.sh sepolia

# 部署到 BSC 测试网
./deploy.sh bsc_testnet

# 部署到 Polygon Mumbai 测试网
./deploy.sh polygon_mumbai

# 部署到主网（请谨慎）
./deploy.sh ethereum
./deploy.sh bsc
./deploy.sh polygon
```

### 方法二：直接使用 forge 命令

```bash
# 部署到 Sepolia 测试网
forge script script/Bridge.s.sol:BridgeScript --rpc-url sepolia --private-key $PRIVATE_KEY --broadcast --verify

# 部署到 BSC 测试网
forge script script/Bridge.s.sol:BridgeScript --rpc-url bsc_testnet --private-key $PRIVATE_KEY --broadcast --verify

# 部署到 Polygon Mumbai 测试网
forge script script/Bridge.s.sol:BridgeScript --rpc-url polygon_mumbai --private-key $PRIVATE_KEY --broadcast --verify
```

## 参数说明

- `--rpc-url`: 区块链网络的 RPC 端点
- `--private-key`: 部署者的私钥
- `--broadcast`: 实际广播交易到网络
- `--verify`: 在区块链浏览器上验证合约源码
- `-vvvv`: 详细日志输出

## 注意事项

1. **私钥安全**: 
   - 绝不要将包含真实私钥的 `.env` 文件提交到代码仓库
   - 建议使用硬件钱包或多签钱包进行主网部署

2. **网络配置**:
   - 测试网部署前请确保钱包有足够的测试币
   - 主网部署前请仔细检查合约代码和配置

3. **合约验证**:
   - 需要配置相应的 API Key 才能自动验证合约
   - 如果验证失败，可以手动在区块链浏览器上验证

## 部署后操作

部署成功后，您需要：

1. **配置白名单**: 调用 `setTokenWhitelist` 添加支持的代币
2. **设置额度**: 调用 `setMaxAmount` 和 `setMinAmount` 设置桥接额度
3. **转移所有权**: 如果需要，调用 `transferOwnership` 转移合约所有权

## 故障排除

- **私钥错误**: 检查私钥格式是否正确（以 0x 开头）
- **RPC 连接失败**: 检查 RPC URL 是否正确，网络是否可达
- **Gas 不足**: 确保钱包有足够的 ETH/BNB/MATIC 支付 Gas 费用
- **合约验证失败**: 检查 API Key 是否正确配置 