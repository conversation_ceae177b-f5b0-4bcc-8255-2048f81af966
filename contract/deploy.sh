#!/bin/bash

# 跨链桥接合约部署脚本
# 使用方法: ./deploy.sh <contract_type> <network>
# 示例: ./deploy.sh bridge sepolia
# 示例: ./deploy.sh token sepolia
# 示例: ./deploy.sh multicall3 bsc_testnet

set -e  # 遇到错误时退出

# 检查是否提供了足够的参数
if [ $# -lt 2 ]; then
    echo "使用方法: $0 <contract_type> <network>"
    echo "合约类型:"
    echo "  bridge     - 部署跨链桥合约"
    echo "  token      - 部署ERC20代币合约"
    echo "  multicall3 - 部署Multicall3合约"
    echo "支持的网络:"
    echo "  brt_testnet    - brt 测试网"
    echo "  bsc_testnet    - BSC 测试网"
    echo "  localhost      - 本地网络"
    exit 1
fi

CONTRACT_TYPE=$1
NETWORK=$2

# 验证合约类型
if [ "$CONTRACT_TYPE" != "bridge" ] && [ "$CONTRACT_TYPE" != "token" ] && [ "$CONTRACT_TYPE" != "multicall3" ]; then
    echo "错误: 不支持的合约类型 '$CONTRACT_TYPE'"
    echo "支持的类型: bridge, token, multicall3"
    exit 1
fi

# 检查 .env 文件是否存在
if [ ! -f .env ]; then
    echo "错误: .env 文件不存在"
    exit 1
fi

# 加载环境变量
source .env

# 检查私钥是否配置
if [ "$PRIVATE_KEY" = "your_private_key_here" ] || [ -z "$PRIVATE_KEY" ]; then
    echo "错误: 请在 .env 文件中配置您的私钥"
    exit 1
fi

# 根据合约类型设置脚本路径
if [ "$CONTRACT_TYPE" = "bridge" ]; then
    SCRIPT_PATH="script/Bridge.s.sol:BridgeScript"
    echo "正在部署跨链桥合约到网络: $NETWORK"
elif [ "$CONTRACT_TYPE" = "token" ]; then
    SCRIPT_PATH="script/Token.s.sol:TokenScript"
    echo "正在部署ERC20代币合约到网络: $NETWORK"
elif [ "$CONTRACT_TYPE" = "multicall3" ]; then
    SCRIPT_PATH="script/Multicall3.s.sol:Multicall3Script"
    echo "正在部署Multicall3合约到网络: $NETWORK"
fi

echo "使用脚本: $SCRIPT_PATH"

# 运行部署命令
forge script $SCRIPT_PATH \
    --rpc-url $NETWORK \
    --private-key $PRIVATE_KEY \
    --broadcast \
    -vvvv

echo "部署完成！"