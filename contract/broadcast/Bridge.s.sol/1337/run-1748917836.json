{"transactions": [{"hash": "0x8a9f8b07b94423df2c52fc1d8993e6eb7fa84c581598f91b6525f9d47240b13f", "transactionType": "CREATE", "contractName": "BridgeContract", "contractAddress": "0x4cc8b224Fe6bD8e6609c155816d35998FFdacAEA", "function": null, "arguments": null, "transaction": {"type": "0x02", "from": "0xcd30f5a62de40428bc9f8223c52f5ae24c3666aa", "gas": "0xd0877", "value": "0x0", "data": "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", "nonce": "0x1e", "accessList": []}, "additionalContracts": [], "isFixedGasLimit": false}], "receipts": [], "libraries": [], "pending": ["0x8a9f8b07b94423df2c52fc1d8993e6eb7fa84c581598f91b6525f9d47240b13f"], "returns": {}, "timestamp": 1748917836, "chain": 1337, "multi": false, "commit": "acb094a"}