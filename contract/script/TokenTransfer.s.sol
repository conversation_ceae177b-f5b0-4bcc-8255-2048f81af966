// SPDX-License-Identifier: MIT
pragma solidity ^0.8.13;

import {Script, console} from "forge-std/Script.sol";
import {EtherERC20} from "../src/EtherERC20.sol";
import {BridgeContract} from "../src/BridgeContract.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";

/**
 * @title TokenTransferScript
 * @dev Comprehensive token transfer script supporting various transfer scenarios
 *
 * Usage:
 * 1. Basic transfer: forge script script/TokenTransfer.s.sol:TokenTransferScript --rpc-url $RPC_URL --private-key $PRIVATE_KEY --broadcast -s "transfer(address,address,uint256)"
 * 2. Batch transfer: forge script script/TokenTransfer.s.sol:TokenTransferScript --rpc-url $RPC_URL --private-key $PRIVATE_KEY --broadcast -s "batchTransfer()"
 * 3. Bridge transfer: forge script script/TokenTransfer.s.sol:TokenTransferScript --rpc-url $RPC_URL --private-key $PRIVATE_KEY --broadcast -s "bridgeTransfer()"
 */
contract TokenTransferScript is Script {
    // State variables
    EtherERC20 public token;
    BridgeContract public bridge;

    // Addresses from environment variables
    address public tokenAddress;
    address public bridgeAddress;

    function setUp() public {
        // Read addresses from environment variables, use zero address as default
        tokenAddress = vm.envOr("TOKEN_ADDRESS", address(0));
        bridgeAddress = vm.envOr("BRIDGE_ADDRESS", address(0));

        console.log("=== Token Transfer Script Configuration ===");
        console.log("Token Address:", tokenAddress);
        console.log("Bridge Address:", bridgeAddress);
        console.log("Executor Address:", msg.sender);
    }

    /**
     * @dev Main execution function - demonstrates various transfer functionalities
     */
    function run() public {
        console.log("\n=== Starting Token Transfer Demo ===");

        vm.startBroadcast();

        // If no token address specified, deploy new token
        if (tokenAddress == address(0)) {
            deployNewToken();
        } else {
            connectToExistingToken();
        }

        // Show basic functionalities
        showTokenInfo();
        demonstrateBasicTransfer();

        vm.stopBroadcast();

        console.log("\n=== Token Transfer Demo Complete ===");
    }

    /**
     * @dev Deploy new token contract
     */
    function deployNewToken() public {
        console.log("\n--- Deploying New Token ---");
        token = new EtherERC20();
        tokenAddress = address(token);
        console.log("New Token Deployed At:", tokenAddress);
        console.log("Token Name:", token.name());
        console.log("Token Symbol:", token.symbol());
        console.log("Initial Supply:", token.totalSupply());
    }

    /**
     * @dev Connect to existing token contract
     */
    function connectToExistingToken() public {
        console.log("\n--- Connecting to Existing Token ---");
        token = EtherERC20(tokenAddress);
        console.log("Token Name:", token.name());
        console.log("Token Symbol:", token.symbol());
        console.log("Current Supply:", token.totalSupply());
    }

    /**
     * @dev Show token basic information
     */
    function showTokenInfo() public view {
        console.log("\n--- Token Information ---");
        console.log("Contract Address:", address(token));
        console.log("Executor Balance:", token.balanceOf(msg.sender));
        console.log("Token Decimals:", token.decimals());
    }

    /**
     * @dev Demonstrate basic transfer functionality
     */
    function demonstrateBasicTransfer() public {
        console.log("\n--- Basic Transfer Demo ---");

        // Create a test recipient address
        address recipient = address(******************************************);
        uint256 amount = 1000 * 10 ** token.decimals(); // 1000 tokens

        uint256 senderBalanceBefore = token.balanceOf(msg.sender);
        uint256 recipientBalanceBefore = token.balanceOf(recipient);

        console.log("Sender Balance Before:", senderBalanceBefore);
        console.log("Recipient Balance Before:", recipientBalanceBefore);

        if (senderBalanceBefore >= amount) {
            token.transfer(recipient, amount);
            console.log("Successfully transferred", amount, "tokens to", recipient);
            console.log("Sender Balance After:", token.balanceOf(msg.sender));
            console.log("Recipient Balance After:", token.balanceOf(recipient));
        } else {
            console.log("Insufficient balance, cannot complete transfer");
        }
    }

    /**
     * @dev Single address transfer
     * @param tokenAddr Token contract address
     * @param to Recipient address
     * @param amount Transfer amount
     */
    function transfer(address tokenAddr, address to, uint256 amount) public {
        console.log("\n=== Executing Single Transfer ===");
        console.log("Token Address:", tokenAddr);
        console.log("Recipient:", to);
        console.log("Amount:", amount);

        vm.startBroadcast();

        IERC20 targetToken = IERC20(tokenAddr);

        // Check balance
        uint256 balance = targetToken.balanceOf(msg.sender);
        require(balance >= amount, "Insufficient balance");

        // Execute transfer
        bool success = targetToken.transfer(to, amount);
        require(success, "Transfer failed");

        console.log("Transfer successful!");
        console.log("New Sender Balance:", targetToken.balanceOf(msg.sender));
        console.log("New Recipient Balance:", targetToken.balanceOf(to));

        vm.stopBroadcast();
    }

    /**
     * @dev Batch transfer functionality
     */
    function batchTransfer() public {
        console.log("\n=== Executing Batch Transfer ===");

        require(tokenAddress != address(0), "Token address not set");

        vm.startBroadcast();

        IERC20 targetToken = IERC20(tokenAddress);

        // Define batch transfer recipients and amounts
        address[] memory recipients = new address[](3);
        uint256[] memory amounts = new uint256[](3);

        recipients[0] = address(0x1111111111111111111111111111111111111111);
        recipients[1] = address(0x2222222222222222222222222222222222222222);
        recipients[2] = address(0x3333333333333333333333333333333333333333);

        uint256 baseAmount = 100 * 10 ** 18; // 100 tokens per recipient
        amounts[0] = baseAmount;
        amounts[1] = baseAmount * 2;
        amounts[2] = baseAmount * 3;

        // Check total balance
        uint256 totalAmount = amounts[0] + amounts[1] + amounts[2];
        uint256 balance = targetToken.balanceOf(msg.sender);
        require(balance >= totalAmount, "Insufficient total balance");

        console.log("Batch Transfer Total Amount:", totalAmount);
        console.log("Current Balance:", balance);

        // Execute batch transfers
        for (uint256 i = 0; i < recipients.length; i++) {
            bool success = targetToken.transfer(recipients[i], amounts[i]);
            require(success, "Transfer to address failed");

            console.log("Transferred to", recipients[i], "amount:", amounts[i]);
        }

        console.log("Batch transfer completed!");
        console.log("Remaining Balance:", targetToken.balanceOf(msg.sender));

        vm.stopBroadcast();
    }

    /**
     * @dev Approval functionality
     * @param tokenAddr Token address
     * @param spender Spender address
     * @param amount Approval amount
     */
    function approve(address tokenAddr, address spender, uint256 amount) public {
        console.log("\n=== Executing Approval ===");
        console.log("Token Address:", tokenAddr);
        console.log("Spender:", spender);
        console.log("Approval Amount:", amount);

        vm.startBroadcast();

        IERC20 targetToken = IERC20(tokenAddr);

        // Execute approval
        bool success = targetToken.approve(spender, amount);
        require(success, "Approval failed");

        console.log("Approval successful!");
        console.log("Current Allowance:", targetToken.allowance(msg.sender, spender));

        vm.stopBroadcast();
    }

    /**
     * @dev Bridge transfer functionality
     */
    function bridgeTransfer() public {
        console.log("\n=== Executing Bridge Transfer ===");

        require(tokenAddress != address(0), "Token address not set");
        require(bridgeAddress != address(0), "Bridge address not set");

        vm.startBroadcast();

        IERC20 targetToken = IERC20(tokenAddress);
        BridgeContract targetBridge = BridgeContract(bridgeAddress);

        uint256 amount = 500 * 10 ** 18; // 500 tokens

        // Check balance
        uint256 balance = targetToken.balanceOf(msg.sender);
        require(balance >= amount, "Insufficient balance");

        // Check if token is whitelisted
        bool isWhitelisted = targetBridge.whitelistedTokens(targetToken);
        console.log("Token Whitelist Status:", isWhitelisted);

        if (!isWhitelisted) {
            console.log("Warning: Token not whitelisted in Bridge, may fail");
        }

        // First approve Bridge contract
        console.log("Approving Bridge contract...");
        bool approveSuccess = targetToken.approve(bridgeAddress, amount);
        require(approveSuccess, "Bridge approval failed");

        console.log("Approval Amount:", amount);
        console.log("Allowance:", targetToken.allowance(msg.sender, bridgeAddress));

        // Execute bridge transfer
        console.log("Executing bridge transfer...");
        targetBridge.bridge(targetToken, amount);

        console.log("Bridge transfer successful!");
        console.log("Remaining Balance:", targetToken.balanceOf(msg.sender));

        vm.stopBroadcast();
    }

    /**
     * @dev Emergency withdraw functionality - withdraw tokens from Bridge contract
     * @param amount Withdrawal amount
     */
    function emergencyWithdraw(uint256 amount) public {
        console.log("\n=== Emergency Withdraw ===");

        require(tokenAddress != address(0), "Token address not set");
        require(bridgeAddress != address(0), "Bridge address not set");

        vm.startBroadcast();

        BridgeContract targetBridge = BridgeContract(bridgeAddress);
        IERC20 targetToken = IERC20(tokenAddress);

        // Check Bridge contract token balance
        uint256 bridgeBalance = targetToken.balanceOf(bridgeAddress);
        console.log("Bridge Contract Token Balance:", bridgeBalance);

        require(bridgeBalance >= amount, "Insufficient Bridge balance");

        // Only owner can execute recovery
        targetBridge.recoverTokens(targetToken, amount);

        console.log("Emergency withdraw successful, amount:", amount);

        vm.stopBroadcast();
    }

    /**
     * @dev Check account balance
     * @param tokenAddr Token address
     * @param account Query address
     */
    function checkBalance(address tokenAddr, address account) public view {
        console.log("\n=== Balance Query ===");
        IERC20 targetToken = IERC20(tokenAddr);
        uint256 balance = targetToken.balanceOf(account);
        console.log("Address:", account);
        console.log("Balance:", balance);
    }

    /**
     * @dev Check allowance
     * @param tokenAddr Token address
     * @param owner Owner address
     * @param spender Spender address
     */
    function checkAllowance(address tokenAddr, address owner, address spender) public view {
        console.log("\n=== Allowance Query ===");
        IERC20 targetToken = IERC20(tokenAddr);
        uint256 allowance = targetToken.allowance(owner, spender);
        console.log("Owner:", owner);
        console.log("Spender:", spender);
        console.log("Allowance:", allowance);
    }

    /**
     * @dev Mint new tokens (EtherERC20 only)
     * @param to Recipient address
     * @param amount Mint amount
     */
    function mintTokens(address to, uint256 amount) public {
        console.log("\n=== Minting Tokens ===");

        require(tokenAddress != address(0), "Token address not set");

        vm.startBroadcast();

        EtherERC20 targetToken = EtherERC20(tokenAddress);

        console.log("Total Supply Before:", targetToken.totalSupply());
        console.log("Recipient Balance Before:", targetToken.balanceOf(to));

        // Execute minting
        targetToken.mint(to, amount);

        console.log("Minting successful!");
        console.log("Total Supply After:", targetToken.totalSupply());
        console.log("Recipient Balance After:", targetToken.balanceOf(to));

        vm.stopBroadcast();
    }
}
