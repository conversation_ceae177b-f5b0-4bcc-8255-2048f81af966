pragma solidity ^0.8.13;

import {Script, console} from "forge-std/Script.sol";
import {EtherERC20} from "../src/EtherERC20.sol";

contract TokenScript is Script {
    EtherERC20 public etherToken;

    function setUp() public {}

    function run() public {
        vm.startBroadcast();

        etherToken = new EtherERC20();

        console.log("EtherERC20 deployed to:", address(etherToken));
        console.log("Token name:", etherToken.name());
        console.log("Token symbol:", etherToken.symbol());
        console.log("Initial supply:", etherToken.totalSupply());

        vm.stopBroadcast();
    }
}
