pragma solidity ^0.8.13;

import {Script, console} from "forge-std/Script.sol";
import {BridgeContract} from "../src/BridgeContract.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";

contract AddWhitelistScript is Script {
    function setUp() public {}

    function run() public {
        // Get addresses from environment variables
        address bridgeAddress = vm.envAddress("BRIDGE_ADDRESS");
        address tokenAddress = vm.envAddress("TOKEN_ADDRESS");

        console.log("Bridge contract address:", bridgeAddress);
        console.log("Token address:", tokenAddress);

        vm.startBroadcast();

        // Connect to deployed Bridge contract
        BridgeContract bridge = BridgeContract(bridgeAddress);

        // Add token to whitelist
        bridge.setTokenWhitelist(IERC20(tokenAddress), true);

        console.log("Successfully added token to whitelist!");

        vm.stopBroadcast();
    }

    // Optional: run with parameters version
    function runWithParams(address bridgeAddress, address tokenAddress) public {
        console.log("Bridge contract address:", bridgeAddress);
        console.log("Token address:", tokenAddress);

        vm.startBroadcast();

        // Connect to deployed Bridge contract
        BridgeContract bridge = BridgeContract(bridgeAddress);

        // Add token to whitelist
        bridge.setTokenWhitelist(IERC20(tokenAddress), true);

        console.log("Successfully added token to whitelist!");

        vm.stopBroadcast();
    }
}
