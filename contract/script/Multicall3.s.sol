// SPDX-License-Identifier: MIT
pragma solidity ^0.8.13;

import {Script, console} from "forge-std/Script.sol";
import {Multicall3} from "../src/Multicall3.sol";

contract Multicall3Script is Script {
    Multicall3 public multicall3;

    function setUp() public {}

    function run() public {
        vm.startBroadcast();

        console.log("Deploying Multicall3 contract...");
        console.log("Deployer address:", msg.sender);

        // Deploy Multicall3 contract
        multicall3 = new Multicall3();

        console.log("Multicall3 deployed to:", address(multicall3));
        console.log("Deployment completed!");

        vm.stopBroadcast();
    }
}
