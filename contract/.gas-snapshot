BridgeContractTest:testBridgeTokens() (gas: 81585)
BridgeContractTest:testCannotBridgeAmountTooLarge() (gas: 48562)
BridgeContractTest:testCannotBridgeAmountTooSmall() (gas: 50776)
BridgeContractTest:testCannotBridgeNonWhitelistedToken() (gas: 1285393)
BridgeContractTest:testCannotBridgeWithInsufficientAllowance() (gas: 52555)
BridgeContractTest:testCannotRedeemNonWhitelistedToken() (gas: 1234112)
BridgeContractTest:testCannotRedeemWithInvalidNonce() (gas: 87928)
BridgeContractTest:testOnlyOwnerCanRecoverTokens() (gas: 16861)
BridgeContractTest:testOnlyOwnerCanRedeem() (gas: 17802)
BridgeContractTest:testOnlyOwnerCanSetMaxAmount() (gas: 17096)
BridgeContractTest:testOnlyOwnerCanSetMinAmount() (gas: 17098)
BridgeContractTest:testOnlyOwnerCanSetWhitelist() (gas: 16960)
BridgeContractTest:testRecoverTokens() (gas: 88110)
BridgeContractTest:testRedeemTokens() (gas: 108743)
BridgeContractTest:testSetMaxAmount() (gas: 28642)
BridgeContractTest:testSetMinAmount() (gas: 28666)
BridgeContractTest:testWhitelistToken() (gas: 1244429)
BridgeContractTest:test_RevertWhen_TransferFailsInRecoverTokens() (gas: 81233)
BridgeContractTest:test_RevertWhen_TransferFailsInRedeem() (gas: 84576)