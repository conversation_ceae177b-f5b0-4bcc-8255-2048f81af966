#!/bin/bash

# Token转账脚本
# 使用方法: ./token_transfer.sh <操作> [参数...] <network>
# 示例: ./token_transfer.sh transfer 0x123...abc 0x456...def 1000 brt_testnet

set -e  # 遇到错误时退出

# 打印使用说明
print_usage() {
    echo "Token转账脚本"
    echo ""
    echo "使用方法: $0 <操作> [参数...] <network>"
    echo ""
    echo "操作:"
    echo "  transfer <token地址> <接收地址> <数量> <network>     - 转账token"
    echo "  approve <token地址> <授权地址> <数量> <network>      - 授权token"
    echo "  balance <token地址> <账户地址> <network>           - 查询余额"
    echo "  mint <接收地址> <数量> <network>                   - 铸造token"
    echo "  bridge <network>                                  - 跨链转账"
    echo ""
    echo "参数说明:"
    echo "  network         - 网络名称 (brt_testnet, bsc_testnet, localhost)"
    echo ""
    echo "环境变量 (在.env文件中配置):"
    echo "  PRIVATE_KEY     - 私钥"
    echo "  TOKEN_ADDRESS   - Token合约地址 (某些操作需要)"
    echo "  BRIDGE_ADDRESS  - Bridge合约地址 (bridge操作需要)"
    echo ""
    echo "示例:"
    echo "  $0 transfer 0x123...abc 0x456...def 1000 brt_testnet"
    echo "  $0 balance 0x123...abc 0x456...def bsc_testnet"
    echo "  $0 bridge localhost"
}

# 检查参数数量
if [ $# -lt 1 ]; then
    print_usage
    exit 1
fi

OPERATION=$1

# 检查 .env 文件是否存在
if [ ! -f .env ]; then
    echo "错误: .env 文件不存在"
    exit 1
fi

# 加载环境变量
source .env

# 检查私钥 (查询操作不需要私钥)
if [ "$OPERATION" != "balance" ] && ([ "$PRIVATE_KEY" = "your_private_key_here" ] || [ -z "$PRIVATE_KEY" ]); then
    echo "错误: 请在 .env 文件中配置您的私钥"
    exit 1
fi

# 执行操作
case "$OPERATION" in
    "transfer")
        if [ $# -ne 5 ]; then
            echo "错误: transfer需要4个参数"
            echo "使用方法: $0 transfer <token地址> <接收地址> <数量> <network>"
            exit 1
        fi
        
        TOKEN_ADDR=$2
        TO_ADDR=$3
        AMOUNT=$4
        NETWORK=$5
        
        echo "=== Token转账 ==="
        echo "Token地址: $TOKEN_ADDR"
        echo "接收地址: $TO_ADDR"
        echo "数量: $AMOUNT"
        echo "网络: $NETWORK"
        echo ""
        
        forge script script/TokenTransfer.s.sol:TokenTransferScript \
            --rpc-url $NETWORK \
            --private-key $PRIVATE_KEY \
            --broadcast \
            -s "transfer(address,address,uint256)" \
            $TOKEN_ADDR $TO_ADDR $AMOUNT \
            -vvvv
        ;;
        
    "approve")
        if [ $# -ne 5 ]; then
            echo "错误: approve需要4个参数"
            echo "使用方法: $0 approve <token地址> <授权地址> <数量> <network>"
            exit 1
        fi
        
        TOKEN_ADDR=$2
        SPENDER_ADDR=$3
        AMOUNT=$4
        NETWORK=$5
        
        echo "=== Token授权 ==="
        echo "Token地址: $TOKEN_ADDR"
        echo "授权地址: $SPENDER_ADDR"
        echo "数量: $AMOUNT"
        echo "网络: $NETWORK"
        echo ""
        
        forge script script/TokenTransfer.s.sol:TokenTransferScript \
            --rpc-url $NETWORK \
            --private-key $PRIVATE_KEY \
            --broadcast \
            -s "approve(address,address,uint256)" \
            $TOKEN_ADDR $SPENDER_ADDR $AMOUNT \
            -vvvv
        ;;
        
    "balance")
        if [ $# -ne 4 ]; then
            echo "错误: balance需要3个参数"
            echo "使用方法: $0 balance <token地址> <账户地址> <network>"
            exit 1
        fi
        
        TOKEN_ADDR=$2
        ACCOUNT_ADDR=$3
        NETWORK=$4
        
        echo "=== 查询余额 ==="
        echo "Token地址: $TOKEN_ADDR"
        echo "账户地址: $ACCOUNT_ADDR"
        echo "网络: $NETWORK"
        echo ""
        
        forge script script/TokenTransfer.s.sol:TokenTransferScript \
            --rpc-url $NETWORK \
            -s "checkBalance(address,address)" \
            $TOKEN_ADDR $ACCOUNT_ADDR \
            -vvvv
        ;;
        
    "mint")
        if [ $# -ne 4 ]; then
            echo "错误: mint需要3个参数"
            echo "使用方法: $0 mint <接收地址> <数量> <network>"
            exit 1
        fi
        
        TO_ADDR=$2
        AMOUNT=$3
        NETWORK=$4
        
        echo "=== 铸造Token ==="
        echo "接收地址: $TO_ADDR"
        echo "数量: $AMOUNT"
        echo "网络: $NETWORK"
        echo ""
        
        forge script script/TokenTransfer.s.sol:TokenTransferScript \
            --rpc-url $NETWORK \
            --private-key $PRIVATE_KEY \
            --broadcast \
            -s "mintTokens(address,uint256)" \
            $TO_ADDR $AMOUNT \
            -vvvv
        ;;
        
    "bridge")
        if [ $# -ne 2 ]; then
            echo "错误: bridge需要1个参数"
            echo "使用方法: $0 bridge <network>"
            exit 1
        fi
        
        NETWORK=$2
        
        if [ -z "$TOKEN_ADDRESS" ] || [ -z "$BRIDGE_ADDRESS" ]; then
            echo "错误: bridge操作需要在.env文件中配置 TOKEN_ADDRESS 和 BRIDGE_ADDRESS"
            exit 1
        fi
        
        echo "=== 跨链转账 ==="
        echo "Token地址: $TOKEN_ADDRESS"
        echo "Bridge地址: $BRIDGE_ADDRESS"
        echo "网络: $NETWORK"
        echo ""
        
        export TOKEN_ADDRESS=$TOKEN_ADDRESS
        export BRIDGE_ADDRESS=$BRIDGE_ADDRESS
        
        forge script script/TokenTransfer.s.sol:TokenTransferScript \
            --rpc-url $NETWORK \
            --private-key $PRIVATE_KEY \
            --broadcast \
            -s "bridgeTransfer()" \
            -vvvv
        ;;
        
    "help"|"-h"|"--help")
        print_usage
        ;;
        
    *)
        echo "错误: 未知操作 '$OPERATION'"
        echo ""
        print_usage
        exit 1
        ;;
esac

echo ""
echo "操作完成！" 