const { ethers } = require('hardhat');
const { setCode } = require('@nomicfoundation/hardhat-network-helpers');
const fs = require('fs');
const path = require('path');

const INSTANCES = {
  entrypoint: {
    address: '******************************************',
    abi: JSON.parse(fs.readFileSync(path.resolve(__dirname, '../bin/EntryPoint070.abi'), 'utf-8')),
    bytecode: fs.readFileSync(path.resolve(__dirname, '../bin/EntryPoint070.bytecode'), 'hex'),
  },
  sendercreator: {
    address: '******************************************',
    abi: JSON.parse(fs.readFileSync(path.resolve(__dirname, '../bin/SenderCreator070.abi'), 'utf-8')),
    bytecode: fs.readFileSync(path.resolve(__dirname, '../bin/SenderCreator070.bytecode'), 'hex'),
  },
};

function deployEntrypoint() {
  return Promise.all(
    Object.entries(INSTANCES).map(([name, { address, abi, bytecode }]) =>
      setCode(address, '0x' + bytecode.replace(/0x/, ''))
        .then(() => ethers.getContractAt(abi, address))
        .then(instance => ({ [name]: instance })),
    ),
  ).then(namedInstances => Object.assign(...namedInstances));
}

module.exports = {
  deployEntrypoint,
};
