{"name": "openzeppelin-solidity", "description": "Secure Smart Contract library for Solidity", "version": "5.2.0", "private": true, "files": ["/contracts/**/*.sol", "!/contracts/mocks/**/*"], "scripts": {"compile": "hardhat compile", "compile:harnesses": "env SRC=./certora/harnesses hardhat compile", "coverage": "scripts/checks/coverage.sh", "docs": "npm run prepare-docs && oz-docs", "docs:watch": "oz-docs watch contracts docs/templates docs/config.js", "prepare": "scripts/prepare.sh", "prepare-docs": "scripts/prepare-docs.sh", "lint": "npm run lint:js && npm run lint:sol", "lint:fix": "npm run lint:js:fix && npm run lint:sol:fix", "lint:js": "prettier --log-level warn --ignore-path .gitignore '**/*.{js,ts}' --check && eslint .", "lint:js:fix": "prettier --log-level warn --ignore-path .gitignore '**/*.{js,ts}' --write && eslint . --fix", "lint:sol": "prettier --log-level warn --ignore-path .gitignore '{contracts,test}/**/*.sol' --check && solhint '{contracts,test}/**/*.sol'", "lint:sol:fix": "prettier --log-level warn --ignore-path .gitignore '{contracts,test}/**/*.sol' --write", "clean": "hardhat clean && rimraf build contracts/build", "prepack": "scripts/prepack.sh", "generate": "scripts/generate/run.js", "version": "scripts/release/version.sh", "test": "hardhat test", "test:generation": "scripts/checks/generation.sh", "test:inheritance": "scripts/checks/inheritance-ordering.js artifacts/build-info/*", "test:pragma": "scripts/checks/pragma-consistency.js artifacts/build-info/*", "gas-report": "env ENABLE_GAS_REPORT=true npm run test", "slither": "npm run clean && slither ."}, "repository": {"type": "git", "url": "https://github.com/OpenZeppelin/openzeppelin-contracts.git"}, "keywords": ["solidity", "ethereum", "smart", "contracts", "security", "zeppelin"], "author": "OpenZeppelin Community <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/OpenZeppelin/openzeppelin-contracts/issues"}, "homepage": "https://openzeppelin.com/contracts/", "devDependencies": {"@changesets/changelog-github": "^0.5.0", "@changesets/cli": "^2.26.0", "@changesets/pre": "^2.0.0", "@changesets/read": "^0.6.0", "@eslint/compat": "^1.2.1", "@nomicfoundation/hardhat-chai-matchers": "^2.0.6", "@nomicfoundation/hardhat-ethers": "^3.0.4", "@nomicfoundation/hardhat-network-helpers": "^1.0.3", "@openzeppelin/docs-utils": "^0.1.5", "@openzeppelin/merkle-tree": "^1.0.7", "@openzeppelin/upgrade-safe-transpiler": "^0.3.32", "@openzeppelin/upgrades-core": "^1.20.6", "chai": "^4.2.0", "eslint": "^9.0.0", "eslint-config-prettier": "^9.0.0", "ethers": "^6.7.1", "globals": "^15.3.0", "glob": "^11.0.0", "graphlib": "^2.1.8", "hardhat": "^2.22.2", "hardhat-exposed": "^0.3.15", "hardhat-gas-reporter": "^2.0.0", "hardhat-ignore-warnings": "^0.2.11", "lodash.startcase": "^4.4.0", "micromatch": "^4.0.2", "p-limit": "^3.1.0", "prettier": "^3.0.0", "prettier-plugin-solidity": "^1.1.0", "rimraf": "^6.0.0", "semver": "^7.3.5", "solhint": "^5.0.0", "solhint-plugin-openzeppelin": "file:scripts/solhint-custom", "solidity-ast": "^0.4.50", "solidity-coverage": "^0.8.5", "solidity-docgen": "^0.6.0-beta.29", "undici": "^6.11.1", "yargs": "^17.0.0"}}