name: Build Indexer Docker Image

on:
  workflow_dispatch:  

env:
  DOCKER_IMAGE_NAME: coinflow/bridge-indexer-dev
  DOCKER_IMAGE_TAG: latest

jobs:
  build-indexer:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./indexer
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest

      - name: Get bun cache directory path
        id: bun-cache-dir-path
        run: echo "dir=$(bun pm cache)" >> $GITHUB_OUTPUT

      - name: Cache Bun dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.bun-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-bun-${{ hashFiles('indexer/bun.lockb') }}
          restore-keys: |
            ${{ runner.os }}-bun-

      - name: Install dependencies
        run: bun install

      - name: Generate Prisma client
        run: |
          # 清理可能的缓存问题
          bun pm cache rm
          # 重新安装依赖
          bun install 
          # 生成 Prisma 客户端
          bunx prisma generate

      - name: Build TypeScript
        run: bun run build

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: indexer-build
          path: ./indexer/dist

  docker-build:
    needs: build-indexer
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: indexer-build
          path: ./indexer/dist

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: ./indexer
          file: ./indexer/Dockerfile
          platforms: linux/amd64,linux/arm64/v8
          push: true
          tags: ${{ env.DOCKER_IMAGE_NAME }}:${{ env.DOCKER_IMAGE_TAG }}
          cache-from: type=registry,ref=${{ env.DOCKER_IMAGE_NAME }}:buildcache
          cache-to: type=registry,ref=${{ env.DOCKER_IMAGE_NAME }}:buildcache,mode=max