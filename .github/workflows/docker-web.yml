name: Build Web Docker Image

on:
  workflow_dispatch:  

env:
  DOCKER_IMAGE_NAME: coinflow/bridge-web-dev
  DOCKER_IMAGE_TAG: latest

jobs:
  build-frontend:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./frontend
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest

      - name: Get bun cache directory path
        id: bun-cache-dir-path
        run: echo "dir=$(bun pm cache)" >> $GITHUB_OUTPUT

      - name: Cache Bun dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.bun-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-bun-${{ hashFiles('frontend/bun.lockb') }}
          restore-keys: |
            ${{ runner.os }}-bun-

      - name: Install dependencies
        run: bun install

      - name: Setup environment file
        run: cp .env.example .env

      - name: Build frontend
        run: bun run build
        env:
          CI: false

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: frontend-build
          path: ./frontend/dist

  docker-build:
    needs: build-frontend
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: frontend-build
          path: ./frontend/dist

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: ./frontend
          file: ./frontend/Dockerfile
          platforms: linux/amd64,linux/arm64/v8
          push: true
          tags: ${{ env.DOCKER_IMAGE_NAME }}:${{ env.DOCKER_IMAGE_TAG }}
          cache-from: type=registry,ref=${{ env.DOCKER_IMAGE_NAME }}:buildcache
          cache-to: type=registry,ref=${{ env.DOCKER_IMAGE_NAME }}:buildcache,mode=max