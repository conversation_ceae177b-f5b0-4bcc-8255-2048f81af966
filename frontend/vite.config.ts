import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

export default defineConfig({
  plugins: [react()],
  server: {
    host: '0.0.0.0',
    port: 5173,
    hmr: {
      port: 24678
    }
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    copyPublicDir: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          wallet: ['viem', 'wagmi', '@reown/appkit']
        }
      }
    }
  },
  publicDir: 'public',
  resolve: {
    alias: {
      'pino': path.resolve(__dirname, './pino-mock.js'),
      'pino-pretty': path.resolve(__dirname, './pino-mock.js'),
      'stream': 'stream-browserify',
      'events': 'events',
      'buffer': 'buffer',
      'process': 'process/browser',
      'util': 'util',
    }
  },
  define: {
    global: 'globalThis',
  },
});