export interface NetworkConfig {
  chainId: number;
  name: string;
  shortName: string;
  symbol: string;
  rpcUrl: string;
  explorerUrl: string;
  icon: string;
  testnet: boolean;
}

export const NETWORKS: Record<string, NetworkConfig> = {
  BRT_TESTNET: {
    chainId: 1337,
    name: 'BRT Testnet',
    shortName: 'BRT',
    symbol: 'BRT',
    rpcUrl: 'https://test-rpc.bitroot.co/',
    explorerUrl: 'https://test-scan.bitroot.co/',
    icon: './bitroot.svg',
    testnet: true,
  },
  BSC_TESTNET: {
    chainId: 97,
    name: 'BSC Testnet',
    shortName: 'BNB',
    symbol: 'tBNB',
    rpcUrl: 'https://data-seed-prebsc-1-s1.bnbchain.org:8545',
    explorerUrl: 'https://testnet.bscscan.com',
    icon: './bnb.png',
    testnet: true,
  },
};

// 获取网络配置的帮助函数
export const getNetworkByChainId = (chainId: number): NetworkConfig | undefined => {
  return Object.values(NETWORKS).find(network => network.chainId === chainId);
};

// 获取explorer链接的帮助函数
export const getExplorerTxUrl = (txHash: string, chainId: number): string => {
  const network = getNetworkByChainId(chainId);
  if (!network) {
    console.warn(`Unknown chainId: ${chainId}`);
    return '#';
  }
  return `${network.explorerUrl}/tx/${txHash}`;
};

// 获取支持的网络列表
export const getSupportedNetworks = (): NetworkConfig[] => {
  return Object.values(NETWORKS);
};

// 检查是否为支持的网络
export const isSupportedNetwork = (chainId: number): boolean => {
  return Object.values(NETWORKS).some(network => network.chainId === chainId);
}; 