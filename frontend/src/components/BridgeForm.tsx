import React, { useState, useEffect } from 'react';
import { addTransaction, updateTransaction } from "../utils/transactionManager";
import { HelpCircle, ArrowDown, CheckCircle } from 'lucide-react';
import { useAccount, useReadContract, useWriteContract, useWaitForTransactionReceipt } from 'wagmi';
import { parseUnits, formatUnits, createPublicClient, http } from 'viem';
import { BNB_BRIDGE, BNB_TOKEN, BRIDGE_ABI, BRIDGE_ABI_BITROOT, BITROOT_BRIDGE, BITROOT_TOKEN, TOKEN_ABI, TOKEN_ABI_BITROOT } from "../utils/constants";
import { NETWORKS, getNetworkByChainId, getExplorerTxUrl } from "../utils/networks";


interface TokenBalance {
    balance: string;
    symbol: string;
}

const BridgeForm = () => {
    const [amount, setAmount] = useState<string>('0.1');
    const [equivalentAmount, setEquivalentAmount] = useState<string>('0.1');
    const [fromToken, setFromToken] = useState<TokenBalance>({ balance: '0', symbol: 'ETK' });
    const [toToken, setToToken] = useState<TokenBalance>({ balance: '0', symbol: 'ETK' });


    const [fromNetwork, setFromNetwork] = useState('BRT');
    const [toNetwork, setToNetwork] = useState('BNB');
    const [isAllowanceSufficient, setIsAllowanceSufficient] = useState(false);
    const [walletConnected, setWalletConnected] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [txHash, setTxHash] = useState('');
    const [errorMessage, setErrorMessage] = useState('');
    const [successMessage, setSuccessMessage] = useState('');


    const { isConnected, chainId, address } = useAccount();
    const [tokenAddress, setTokenAddress] = useState(BITROOT_TOKEN);
    const [spenderAddress, setSpenderAddress] = useState(BITROOT_BRIDGE);
    const [currentTokenAbi, setCurrentTokenAbi] = useState(TOKEN_ABI_BITROOT);
    const [currentBridgeAbi, setCurrentBridgeAbi] = useState(BRIDGE_ABI_BITROOT);


    const { writeContractAsync } = useWriteContract();
    const { data: txReceipt, isLoading: isWaiting, isSuccess: isConfirmed } =
        // @ts-ignore
        useWaitForTransactionReceipt({
            hash: txHash as `0x${string}`,
        });


    useEffect(() => {
        setEquivalentAmount(amount);
    }, [amount]);


    useEffect(() => {
        if (!isConnected) return;
        console.log("Chain ID detected:", chainId);

        if (chainId === NETWORKS.BSC_TESTNET.chainId) {  // BSC Testnet
            console.log("Setting up for BSC Testnet");
            setFromNetwork('BNB');
            setToNetwork('BRT');
            setTokenAddress(BNB_TOKEN);
            setSpenderAddress(BNB_BRIDGE);
            setCurrentTokenAbi(TOKEN_ABI);
            setCurrentBridgeAbi(BRIDGE_ABI);
        } else if (chainId === NETWORKS.BRT_TESTNET.chainId) {  // BRT Testnet
            console.log("Setting up for BRT Testnet");
            setFromNetwork('BRT');
            setToNetwork('BNB');
            setTokenAddress(BITROOT_TOKEN);
            setSpenderAddress(BITROOT_BRIDGE);
            setCurrentTokenAbi(TOKEN_ABI_BITROOT);
            setCurrentBridgeAbi(BRIDGE_ABI_BITROOT);
        } else {
            console.log("Unsupported chain, defaulting to BSC");
            setFromNetwork('BNB');
            setToNetwork('BRT');
            setTokenAddress(BNB_TOKEN);
            setSpenderAddress(BNB_BRIDGE);
            setCurrentTokenAbi(TOKEN_ABI);
            setCurrentBridgeAbi(BRIDGE_ABI);

            if (chainId) {
                const network = getNetworkByChainId(chainId);
                if (!network) {
                    console.warn(`Network with chainId ${chainId} is not supported. Please switch to BSC Testnet or BRT Testnet.`);
                }
            }
        }
    }, [chainId, isConnected]);


    const { data: isWhitelisted } = useReadContract({
        address: spenderAddress as `0x${string}`,
        abi: currentBridgeAbi,
        functionName: 'whitelistedTokens',
        args: [tokenAddress],
        query: {
            enabled: isConnected && !!tokenAddress && !!spenderAddress,
        }
    });


    const { data: minAmount } = useReadContract({
        address: spenderAddress as `0x${string}`,
        abi: currentBridgeAbi,
        functionName: 'minAmounts',
        args: [tokenAddress],
        query: {
            enabled: isConnected && !!tokenAddress && !!spenderAddress,
        }
    });


    const { data: maxAmount } = useReadContract({
        address: spenderAddress as `0x${string}`,
        abi: currentBridgeAbi,
        functionName: 'maxAmounts',
        args: [tokenAddress],
        query: {
            enabled: isConnected && !!tokenAddress && !!spenderAddress,
        }
    });


    const { data: allowance, refetch } = useReadContract({
        address: tokenAddress as `0x${string}`,
        abi: currentTokenAbi,
        functionName: 'allowance',
        args: [address, spenderAddress],
        query: {
            enabled: isConnected && !!address && !!tokenAddress && !!spenderAddress,
        }
    });


    const { data: tokenBalance, refetch: refetchBalance, isLoading: isBalanceLoading } = useReadContract({
        address: tokenAddress as `0x${string}`,
        abi: currentTokenAbi,
        functionName: 'balanceOf',
        args: [address],
        query: {
            enabled: isConnected && !!address && !!tokenAddress,
        }
    });


    useEffect(() => {
        if (tokenBalance !== undefined) {
            setFromToken(prev => ({
                ...prev,
                balance: formatUnits(tokenBalance as bigint, 18)
            }));
        } else if (isConnected && !isBalanceLoading) {
            // 如果连接了钱包但没有余额数据，可能是网络问题或合约不存在
            setFromToken(prev => ({
                ...prev,
                balance: '0'
            }));
        }
    }, [tokenBalance, isConnected, isBalanceLoading]);


    useEffect(() => {
        if (!amount || !allowance) {
            setIsAllowanceSufficient(false);
            return;
        }
        try {
            const amountBigInt = parseUnits(amount.toString(), 18);
            const allowanceBigInt = allowance as bigint;

            const isSufficient = allowanceBigInt >= amountBigInt;
            setIsAllowanceSufficient(isSufficient);
            
            console.log("Allowance check:", {
                allowance: formatUnits(allowanceBigInt, 18),
                amount: amount,
                isSufficient: isSufficient,
                allowanceBigInt: allowanceBigInt.toString(),
                amountBigInt: amountBigInt.toString()
            });
        } catch (error) {
            console.error("Error checking allowance:", error);
            setIsAllowanceSufficient(false);
        }
    }, [allowance, amount]);

    // 监听 allowance 变化，确保状态及时更新
    useEffect(() => {
        console.log("Allowance changed:", allowance);
        if (allowance !== undefined) {
            // 强制重新检查 allowance 是否足够
            setTimeout(() => {
                refetch();
            }, 1000);
        }
    }, [allowance, refetch]);

    useEffect(() => {
        setWalletConnected(isConnected);

        if (isConnected) {
            console.log("Wallet connected:", address);
        } else {
            console.log("Wallet disconnected");
            setIsAllowanceSufficient(false);
            setSuccessMessage('');
        }
    }, [isConnected, address]);


    useEffect(() => {
        if (isConfirmed && txReceipt) {
            console.log("Transaction confirmed:", txReceipt);

            if (txHash) {
                const status = txReceipt.status === 'success' ? 'completed' : 'failed';
                updateTransaction(txHash, {
                    status: status,
                    blockNumber: txReceipt.blockNumber,
                    blockHash: txReceipt.blockHash
                });
            }

            // 重新获取 allowance 和余额
            refetch();
            refetchBalance();

            if (txReceipt.status === 'success') {
                // 判断是 Approval 还是 Bridge 交易
                const isApprovalTx = txReceipt.logs.some(log => 
                    log.topics[0] === '0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925' // Approval 事件签名
                );
                
                const actionType = isApprovalTx ? 'Approval' : 'Bridge';
                setSuccessMessage(`${actionType} transaction successful! Your transaction has been confirmed.`);

                // 如果是 Approval 交易成功，延迟一下再检查 allowance
                if (isApprovalTx) {
                    setTimeout(() => {
                        refetch(); // 再次刷新 allowance
                    }, 2000);
                } else {
                    setAmount('');
                    setEquivalentAmount('');
                }
            }

            setIsLoading(false);

            const timer = setTimeout(() => {
                setSuccessMessage('');
            }, 10000);

            return () => clearTimeout(timer);
        }
    }, [isConfirmed, txReceipt, txHash, refetch, refetchBalance]);


    const validateInputs = () => {
        if (!isConnected) {
            alert("Please connect your wallet first");
            return false;
        }

        if (!amount || Number(amount) <= 0) {
            alert("Please enter a valid amount");
            return false;
        }

        if (!tokenAddress || !spenderAddress) {
            alert("Contract addresses not properly configured");
            return false;
        }

        if (isWhitelisted === false) {
            setErrorMessage(`Token ${tokenAddress} is not whitelisted for bridging`);
            return false;
        }

        if (minAmount) {
            const amountBigInt = parseUnits(amount.toString(), 18);
            const minAmountBigInt = minAmount as bigint;
            if (amountBigInt < minAmountBigInt) {
                setErrorMessage(`Amount is too small. Minimum required: ${formatUnits(minAmountBigInt, 18)}`);
                return false;
            }
        }

        if (maxAmount && (maxAmount as bigint) > 0n) {
            const amountBigInt = parseUnits(amount.toString(), 18);
            const maxAmountBigInt = maxAmount as bigint;
            if (amountBigInt > maxAmountBigInt) {
                setErrorMessage(`Amount is too large. Maximum allowed: ${formatUnits(maxAmountBigInt, 18)}`);
                return false;
            }
        }

        return true;
    };


    const approve = async () => {
        if (!validateInputs()) return;

        setIsLoading(true);
        setErrorMessage('');
        setSuccessMessage('');
        setTxHash('');

        try {
            const approveAmount = parseUnits(amount.toString(), 18);

            console.log(`Approving ${amount} tokens from ${tokenAddress} to ${spenderAddress}`);

            // @ts-ignore
            const hash = await writeContractAsync({
                address: tokenAddress as `0x${string}`,
                abi: currentTokenAbi,
                functionName: 'approve',
                args: [spenderAddress, approveAmount],
            });

            setTxHash(hash);
            console.log("Approval transaction submitted:", hash);

            addTransaction({
                hash: hash,
                type: 'Approve',
                status: 'pending',
                amount: amount,
                token: 'ETK',
                chainId: chainId,
                userAddress: address
            });

        } catch (error) {
            console.error("Approval error:", error);
            setErrorMessage(error instanceof Error ? error.message : String(error));
            setIsLoading(false);
        }
    };


    const handleSwap = async () => {
        if (!validateInputs() || !isAllowanceSufficient) return;

        setIsLoading(true);
        setErrorMessage('');
        setSuccessMessage('');
        setTxHash('');

        try {
            const bridgeAmount = parseUnits(amount.toString(), 18);

            console.log(`Bridging ${amount} tokens from ${fromNetwork} to ${toNetwork}`);
            console.log(`Token: ${tokenAddress}, Bridge: ${spenderAddress}`);

            const gasLimit = chainId === NETWORKS.BRT_TESTNET.chainId ? BigInt(1000000) : BigInt(500000);

            // @ts-ignore
            const hash = await writeContractAsync({
                address: spenderAddress as `0x${string}`,
                abi: currentBridgeAbi,
                functionName: 'bridge',
                args: [tokenAddress, bridgeAmount],
                gas: gasLimit
            });

            setTxHash(hash);
            console.log("Bridge transaction submitted:", hash);

            addTransaction({
                hash: hash,
                type: 'Bridge',
                status: 'pending',
                amount: amount,
                token: 'ETK',
                fromNetwork: fromNetwork,
                toNetwork: toNetwork,
                chainId: chainId,
                userAddress: address
            });

        } catch (error) {
            console.error("Bridge error:", error);

            const errorString = String(error);

            if (errorString.includes("Token_Not_Whitelisted")) {
                setErrorMessage("This token is not whitelisted for bridging");
            } else if (errorString.includes("Amount_Too_Small")) {
                setErrorMessage(`Amount is too small. Minimum required: ${minAmount ? formatUnits(minAmount as bigint, 18) : 'unknown'}`);
            } else if (errorString.includes("Amount_Too_Large")) {
                setErrorMessage(`Amount is too large. Maximum allowed: ${maxAmount ? formatUnits(maxAmount as bigint, 18) : 'unknown'}`);
            } else if (errorString.includes("Insufficient_Allowance")) {
                setErrorMessage("Insufficient allowance. Please approve more tokens.");
                setIsAllowanceSufficient(false);
            } else {
                setErrorMessage(errorString);
            }

            setIsLoading(false);
        }
    };


    // const handlePercentage = (percentage: number) => {
    //   if (!tokenBalance) return;


    //   const calculatedAmount = (Number(formatUnits(tokenBalance, 18)) * percentage / 100).toString();
    //   setAmount(calculatedAmount);
    // };


    // const handleMax = () => {
    //   if (!tokenBalance) return;
    //   setAmount(formatUnits(tokenBalance, 18));
    // };


    const handleClearForm = () => {
        setAmount('');
        setEquivalentAmount('');
        setErrorMessage('');
        setSuccessMessage('');
        setTxHash('');
    };

    console.log(errorMessage);

    // 读取当前网络的 token 符号
    const { data: fromTokenSymbol, error: symbolError, isLoading: symbolLoading } = useReadContract({
        address: tokenAddress as `0x${string}`,
        abi: currentTokenAbi,
        functionName: 'symbol',
        query: {
            enabled: isConnected && !!tokenAddress,
        }
    });

    // 同时读取 token 的 name 来确认是否读取了正确的合约
    const { data: fromTokenName } = useReadContract({
        address: tokenAddress as `0x${string}`,
        abi: currentTokenAbi,
        functionName: 'name',
        query: {
            enabled: isConnected && !!tokenAddress,
        }
    });



    // 读取目标网络的 token 符号和余额
    const targetTokenAddress = chainId === NETWORKS.BSC_TESTNET.chainId ? BITROOT_TOKEN : BNB_TOKEN;
    const targetTokenAbi = chainId === NETWORKS.BSC_TESTNET.chainId ? TOKEN_ABI_BITROOT : TOKEN_ABI;
    const targetChainId = chainId === NETWORKS.BSC_TESTNET.chainId ? NETWORKS.BRT_TESTNET.chainId : NETWORKS.BSC_TESTNET.chainId;

    // 创建目标网络的 provider
    const targetProvider = createPublicClient({
        chain: targetChainId === NETWORKS.BRT_TESTNET.chainId ? {
            id: NETWORKS.BRT_TESTNET.chainId,
            name: NETWORKS.BRT_TESTNET.name,
            nativeCurrency: { name: NETWORKS.BRT_TESTNET.symbol, symbol: NETWORKS.BRT_TESTNET.symbol, decimals: 18 },
            rpcUrls: { default: { http: [import.meta.env.VITE_AMOY_RPC || NETWORKS.BRT_TESTNET.rpcUrl] } },
            blockExplorers: { default: { name: 'BRTScan', url: NETWORKS.BRT_TESTNET.explorerUrl } },
            testnet: true
        } : {
            id: NETWORKS.BSC_TESTNET.chainId,
            name: NETWORKS.BSC_TESTNET.name,
            nativeCurrency: { name: NETWORKS.BSC_TESTNET.symbol, symbol: NETWORKS.BSC_TESTNET.symbol, decimals: 18 },
            rpcUrls: { default: { http: [import.meta.env.VITE_BNB_RPC || NETWORKS.BSC_TESTNET.rpcUrl] } },
            blockExplorers: { default: { name: 'BscScan', url: NETWORKS.BSC_TESTNET.explorerUrl } },
            testnet: true
        },
        transport: http()
    });

    // 使用 wagmi 的 useReadContract 查询目标网络的 token 符号
    const { data: toTokenSymbol } = useReadContract({
        address: targetTokenAddress as `0x${string}`,
        abi: targetTokenAbi,
        functionName: 'symbol',
        chainId: targetChainId,
        query: {
            enabled: isConnected && !!targetTokenAddress,
        }
    });

    // 自定义查询目标网络的 token 余额
    const [toTokenBalance, setToTokenBalance] = useState<bigint | undefined>(undefined);
    const [isToBalanceLoading, setIsToBalanceLoading] = useState(false);

    useEffect(() => {
        const fetchTargetBalance = async () => {
            if (!isConnected || !address || !targetTokenAddress) {
                setToTokenBalance(undefined);
                return;
            }

            setIsToBalanceLoading(true);
            try {
                const balance = await targetProvider.readContract({
                    address: targetTokenAddress as `0x${string}`,
                    abi: targetTokenAbi,
                    functionName: 'balanceOf',
                    args: [address]
                });
                setToTokenBalance(balance as bigint);
                console.log(`Target balance fetched: ${formatUnits(balance as bigint, 18)}`);
            } catch (error) {
                console.error('Error fetching target balance:', error);
                setToTokenBalance(undefined);
            } finally {
                setIsToBalanceLoading(false);
            }
        };

        fetchTargetBalance();
    }, [isConnected, address, targetTokenAddress, targetProvider, targetTokenAbi]);

    // 更新 token 符号和余额
    useEffect(() => {
        if (fromTokenSymbol) {
            setFromToken(prev => ({
                ...prev,
                symbol: fromTokenSymbol as string
            }));
        }

        if (toTokenSymbol) {
            setToToken(prev => ({
                ...prev,
                symbol: toTokenSymbol as string
            }));
        }

        // 更新目标网络的 token 余额
        if (toTokenBalance !== undefined) {
            setToToken(prev => ({
                ...prev,
                balance: formatUnits(toTokenBalance as bigint, 18)
            }));
        } else if (isConnected && !isToBalanceLoading) {
            // 如果连接了钱包但没有余额数据，可能是网络问题或合约不存在
            setToToken(prev => ({
                ...prev,
                balance: '0'
            }));
        }
    }, [fromTokenSymbol, toTokenSymbol, toTokenBalance, isConnected, isToBalanceLoading]);

    // 添加详细的调试日志
    useEffect(() => {
        console.log('=== Token Debug Info ===');
        console.log('chainId:', chainId);
        console.log('targetChainId:', targetChainId);
        console.log('tokenAddress:', tokenAddress);
        console.log('targetTokenAddress:', targetTokenAddress);
        console.log('BNB_TOKEN:', BNB_TOKEN);
        console.log('BITROOT_TOKEN:', BITROOT_TOKEN);
        console.log('fromTokenSymbol from contract:', fromTokenSymbol);
        console.log('fromTokenName from contract:', fromTokenName);
        console.log('toTokenSymbol from contract:', toTokenSymbol);
        console.log('toTokenBalance from contract:', toTokenBalance);
        console.log('symbolError:', symbolError);
        console.log('symbolLoading:', symbolLoading);
        console.log('isToBalanceLoading:', isToBalanceLoading);
        console.log('current fromToken.symbol:', fromToken.symbol);
        console.log('current toToken.symbol:', toToken.symbol);
        console.log('current toToken.balance:', toToken.balance);
        console.log('targetProvider chain:', targetProvider.chain?.name);
        console.log('========================');
    }, [chainId, targetChainId, tokenAddress, targetTokenAddress, fromTokenSymbol, fromTokenName, toTokenSymbol, toTokenBalance, symbolError, symbolLoading, isToBalanceLoading, fromToken.symbol, toToken.symbol, toToken.balance, targetProvider]);

    return (
        <div className="bg-[#d6a4a4] rounded-3xl shadow-sm border border-gray-200 p-5 md:p-6 w-full h-auto">
            <div className="flex flex-col h-full">
                <h1 className="text-xl font-bold mb-4">Bridge</h1>
                <div className="mb-3">
                    <label className="text-gray-600 mb-2 block text-sm">Transfer from</label>
                    <div className="bg-[#d6a4a4] rounded-xl p-2 md:p-2">
                        <div className="flex items-center justify-between mb-3 flex-wrap gap-2">
                            <div className="flex items-center gap-2">
                                <div className="w-7 h-7 bg-[#d6a4a4] rounded-full">
                                    <img src={fromNetwork === 'BRT' ? "./bitroot.svg" : "./bnb.png"} alt={fromNetwork} className="w-7 h-7" />
                                </div>
                                <span className="font-medium text-sm">{fromNetwork}</span>
                            </div>
                            <div className="text-xs text-gray-600">
                                Balance: {isBalanceLoading ? 'Loading...' : fromToken.balance}
                            </div>
                        </div>
                        <div className="flex gap-2 rounded-xl p-3 border border-gray-300 shadow-md hover:shadow-lg transition-shadow">
                            <button className="w-24 flex items-center justify-between gap-1 px-3 py-2 bg-[#d6a4a4] rounded-lg border border-gray-200 text-sm">
                                <span className="font-medium">{fromToken.symbol}</span>
                            </button>
                            <input
                                type="text"
                                value={amount}
                                onChange={(e) => setAmount(e.target.value)}
                                className="flex-1 bg-[#d6a4a4] rounded-lg px-3 py-2 text-right text-sm"
                                placeholder="0.0"
                            />
                        </div>
                        {/* Remove Calculation Percentage From UI
            <div className="flex gap-2 mt-3">
              <button
                onClick={() => handlePercentage(25)}
                className="px-2 py-1 rounded-lg border border-gray-200 text-xs hover:bg-gray-100 transition-colors"
              >
                25%
              </button>
              <button
                onClick={() => handlePercentage(50)}
                className="px-2 py-1 rounded-lg border border-gray-200 text-xs hover:bg-gray-100 transition-colors"
              >
                50%
              </button>
              <button
                onClick={handleMax}
                className="px-2 py-1 rounded-lg border border-gray-200 text-xs hover:bg-gray-100 transition-colors"
              >
                MAX
              </button> 
            </div> */}
                    </div>
                </div>
                <div className="flex justify-center my-2">
                    <div className="w-8 h-8 bg-[#d6a4a4] rounded-full flex items-center justify-center">
                        <ArrowDown size={18} className="text-gray-700" />
                    </div>
                </div>
                <div className="mb-4">
                    <label className="text-gray-600 mb-2 block text-sm">Transfer To</label>
                    <div className="bg-[#d6a4a4] rounded-xl p-3 md:p-4 border border-gray-300 shadow-md hover:shadow-lg transition-shadow">
                        <div className="flex items-center justify-between flex-wrap gap-2 mb-3">
                            <div className="flex items-center gap-2 w-24">
                                <img src={toNetwork === 'BNB' ? "./bnb.png" : "./bitroot.svg"} alt={toNetwork} className="w-7 h-7" />
                                <span className="font-medium text-sm">{toNetwork}</span>
                            </div>
                            <div className="text-xs text-gray-600">
                                Balance: {isToBalanceLoading ? 'Loading...' : toToken.balance}
                            </div>
                        </div>

                        <div className="flex gap-2 rounded-xl p-3 border border-gray-300 bg-[#d6a4a4]/50">
                            <div className="w-24 flex items-center gap-1 px-3 py-2 bg-[#d6a4a4] rounded-lg border border-gray-200 text-sm">
                                <span className="font-medium">{toToken.symbol}</span>
                            </div>
                            <div className="flex-1 bg-[#d6a4a4]/30 rounded-lg px-3 py-2 text-right text-sm">
                                {equivalentAmount || '0.0'}
                            </div>
                        </div>
                        <div className="mt-2 text-xs text-gray-600 italic text-right">
                            Estimated amount you will receive
                        </div>
                    </div>
                </div>

                {successMessage && (
                    <div className="mb-4 p-3 bg-green-100 text-green-700 rounded-lg text-sm border border-green-200 flex items-start gap-2">
                        <CheckCircle size={18} className="mt-0.5 flex-shrink-0" />
                        <div>
                            <p className="font-medium">Success!</p>
                            <p>{successMessage}</p>
                        </div>
                    </div>
                )}

                {isConnected && (
                    <div className="p-3 bg-[#d6a4a4]/50 rounded-lg text-xs mb-4 border border-gray-300">
                        <div className="flex flex-col gap-1">
                            {isWhitelisted !== undefined && (
                                <div className="flex justify-between">
                                    <span>Token Whitelisted:</span>
                                    <span>{isWhitelisted ? 'Yes' : 'No'}</span>
                                </div>
                            )}
                            {minAmount && (
                                <div className="flex justify-between">
                                    <span>Min Amount:</span>
                                    <span>{formatUnits(minAmount as bigint, 18)}</span>
                                </div>
                            )}
                            {maxAmount && (
                                <div className="flex justify-between">
                                    <span>Max Amount:</span>
                                    <span>{formatUnits(maxAmount as bigint, 18)}</span>
                                </div>
                            )}
                            {allowance && (
                                <div className="flex justify-between">
                                    <span>Current Allowance:</span>
                                    <span>{formatUnits(allowance as bigint, 18)}</span>
                                </div>
                            )}
                        </div>
                    </div>
                )}

                <div className="flex-1 min-h-[15px]"></div>
                <div className="space-y-2 mb-4">
                    <div className="flex items-center justify-between text-xs">
                        <div className="flex items-center gap-1">
                            Est. Gas Fees <HelpCircle size={14} className="text-gray-700" />
                        </div>
                        <div className="flex items-end gap-1">
                            <span>0.0003 {fromToken.symbol}</span>
                            <span className="text-gray-700 hidden sm:inline">~1.66 USD</span>
                        </div>
                    </div>
                    <div className="flex items-center justify-between text-xs">
                        <div className="flex items-center gap-1">
                            Est. Time <HelpCircle size={14} className="text-gray-700" />
                        </div>
                        <span>9 secs</span>
                    </div>
                </div>


                {txHash && (
                    <div className="mb-4 p-3 bg-[#d6a4a4]/50 rounded-lg text-xs border border-gray-300">
                        <p className="font-medium">Transaction {isWaiting ? 'Processing' : 'Confirmed'}:</p>
                        <a
                            href={getExplorerTxUrl(txHash, chainId || 0)}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-700 break-all hover:underline"
                        >
                            {txHash}
                        </a>
                    </div>
                )}

                {errorMessage && (
                    <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-lg text-xs border border-red-200 flex items-start gap-2 max-w-full overflow-hidden">
                        <div className="overflow-x-auto break-words whitespace-pre-wrap max-w-[90%]">
                            <p className="font-medium">Error:</p>
                            <p>{errorMessage}</p>
                        </div>
                        <button
                            onClick={() => setErrorMessage('')}
                            className="ml-auto text-red-500 hover:text-red-700"
                        >
                            ✕
                        </button>
                    </div>
                )}

                {/* 调试信息 */}
                {isConnected && (
                    <div className="mb-2 p-2 bg-gray-100 rounded text-xs">
                        <div>Debug: isAllowanceSufficient = {isAllowanceSufficient.toString()}</div>
                        <div>Debug: allowance = {allowance ? formatUnits(allowance as bigint, 18) : 'undefined'}</div>
                        <div>Debug: amount = {amount}</div>
                        <div>Debug: isConnected = {isConnected.toString()}</div>
                    </div>
                )}

                {isConnected && isAllowanceSufficient ? (
                    <button
                        onClick={handleSwap}
                        disabled={!walletConnected || !isAllowanceSufficient || isLoading || isWaiting || !amount}
                        className={`w-full bg-custom-gradient text-gray-700 py-3 rounded-xl font-medium transition-all text-sm shadow-md hover:shadow-lg ${(!amount || isLoading || isWaiting) ? 'opacity-70 cursor-not-allowed' : 'hover:bg-[#d6a4a4]'}`}
                    >
                        {isLoading || isWaiting ? 'Processing...' : `Bridge ${fromNetwork} to ${toNetwork}`}
                    </button>
                ) : (
                    <button
                        onClick={approve}
                        disabled={!walletConnected || isLoading || isWaiting || !amount}
                        className={`w-full text-white py-3 rounded-xl font-medium transition-all text-sm shadow-md hover:shadow-lg ${(!amount || isLoading || isWaiting) ? 'opacity-70 cursor-not-allowed' : 'hover:bg-yellow-600'}
          ${toNetwork === "BNB" ? "bg-yellow-500" : "bg-purple-700"}`
                        }
                    >
                        {isLoading || isWaiting ? 'Processing...' : 'Approve'}
                    </button>
                )}

                {(amount || errorMessage || successMessage || txHash) && (
                    <button
                        onClick={handleClearForm}
                        className="w-full mt-2 bg-transparent text-gray-600 py-2 rounded-xl font-medium text-xs hover:bg-[#dae2f8] transition-colors"
                    >
                        Clear Form
                    </button>
                )}


                <div className="flex justify-between items-center mt-3 text-xs text-gray-500">
                    <span>1. Bridge</span>
                    <span>2. Claim</span>
                </div>
            </div>
        </div>
    );
};


export default BridgeForm;