import { WagmiProvider, createConfig, http } from "wagmi";
import { bscTestnet } from "wagmi/chains";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ConnectKitProvider, getDefaultConfig } from "connectkit";
import { define<PERSON>hain } from "viem";
import { NETWORKS } from "../utils/networks";

// 使用统一配置定义 BRT Testnet 链
const customBRTTestnet = defineChain({
    id: NETWORKS.BRT_TESTNET.chainId,
    name: NETWORKS.BRT_TESTNET.name,
    nativeCurrency: { 
        name: NETWORKS.BRT_TESTNET.symbol, 
        symbol: NETWORKS.BRT_TESTNET.symbol, 
        decimals: 18 
    },
    rpcUrls: {
        default: {
            http: [import.meta.env.VITE_AMOY_RPC || NETWORKS.BRT_TESTNET.rpcUrl],
        },
        public: {
            http: [import.meta.env.VITE_AMOY_RPC || NETWORKS.BRT_TESTNET.rpcUrl],
        },
    },
    blockExplorers: {
        default: {
            name: 'BRTS<PERSON>',
            url: NETWORKS.BRT_TESTNET.explorerUrl,
            apiUrl: `${NETWORKS.BRT_TESTNET.explorerUrl}/api`,
        },
    },
    contracts: {
        multicall3: {
            address: '0x422dbCa8247De377fFA7858c58725a6853a3264f',
            blockCreated: 1258855,
        },
    },
    testnet: true,
});

// 使用统一配置定义 BSC Testnet 链
const customBscTestnet = defineChain({
    id: NETWORKS.BSC_TESTNET.chainId,
    name: NETWORKS.BSC_TESTNET.name,
    nativeCurrency: {
        decimals: 18,
        name: NETWORKS.BSC_TESTNET.symbol,
        symbol: NETWORKS.BSC_TESTNET.symbol,
    },
    rpcUrls: {
        default: { 
            http: [import.meta.env.VITE_BNB_RPC || NETWORKS.BSC_TESTNET.rpcUrl] 
        },
        public: { 
            http: [import.meta.env.VITE_BNB_RPC || NETWORKS.BSC_TESTNET.rpcUrl] 
        },
    },
    blockExplorers: {
        default: {
            name: 'BscScan',
            url: NETWORKS.BSC_TESTNET.explorerUrl,
            apiUrl: `${NETWORKS.BSC_TESTNET.explorerUrl}/api`,
        },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 17422483,
        },
    },
    testnet: true,
});

const config = createConfig(
    getDefaultConfig({
        chains: [customBRTTestnet, customBscTestnet],
        transports: {
            [customBRTTestnet.id]: http(import.meta.env.VITE_AMOY_RPC || NETWORKS.BRT_TESTNET.rpcUrl),
            [customBscTestnet.id]: http(import.meta.env.VITE_BNB_RPC || NETWORKS.BSC_TESTNET.rpcUrl),
        },
        walletConnectProjectId: import.meta.env.VITE_API_PROJECTID,
        appName: import.meta.env.VITE_API_PROJECT_NAME,
        appDescription: import.meta.env.VITE_API_PROJECT_DESC
    }),
);

const queryClient = new QueryClient();

export const Web3Provider = ({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) => {
    return (
        <WagmiProvider config={config}>
            <QueryClientProvider client={queryClient}>
                <ConnectKitProvider customTheme={{
                    "--ck-body-background": `#d6a4a4`,
                    "--ck-body-color": "#323232",
                    "--ck-body-color-muted": "#323232",
                    "--ck-primary-button-background": `#dae2f8`,
                    "--ck-primary-button-color": "#323232",
                    "--ck-primary-button-hover-background": "#dae2f8",
                    "--ck-secondary-button-background": "#dae2f8",
                    "--ck-secondary-button-hover-background": "#dae2f8",
                    "--ck-tooltip-background": "#dae2f8",
                    "--ck-secondary-button-color": "#323232",
                    "--ck-body-action-color": "#dae2f8",
                }}>
                    {children}
                </ConnectKitProvider>
            </QueryClientProvider>
        </WagmiProvider>
    );
};
