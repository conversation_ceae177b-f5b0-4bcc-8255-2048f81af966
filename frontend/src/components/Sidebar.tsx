import React from 'react';
import { Wallet, Grid as BridgeIcon, FileText, Activity, Radio, Download, HelpCircle, FileCode, X, Search } from 'lucide-react';
import { ConnectKitButton } from 'connectkit';
import { useAccount } from 'wagmi';
import { useNavigate, useLocation } from 'react-router-dom';

const menuItems = [
  { icon: Wallet, text: 'Wallet', path: null },
  { icon: BridgeIcon, text: 'Bridge', path: '/' },
  { icon: Search, text: 'Transaction Hash', path: '/transaction-hash' },
  { icon: FileText, text: 'Transactions', path: null },
  { icon: Activity, text: 'Network status', path: null },
  { icon: Download, text: 'Get testnet ETH', path: null },
  { icon: HelpCircle, text: 'Support', path: null },
  { icon: FileText, text: 'FAQs', path: null },
  { icon: FileCode, text: 'Documentation', path: null },
];

const Sidebar = ({ onClose }: any) => {
  const { isConnected } = useAccount();
  const navigate = useNavigate();
  const location = useLocation();

  const handleItemClick = (clickedIndex: number, path: string | null) => {
    if (path) {
      navigate(path);
      if (onClose) onClose(); // 移动端点击后自动关闭菜单
    }
  };

  const CustomConnectButton = ({ isActive }: { isActive: boolean }) => {
    return (
      <ConnectKitButton.Custom>
        {({ isConnected, show }) => {
          return (
            <button
              onClick={(e) => {
                e.stopPropagation();
                show();
              }}
              className="text-sm w-full text-left"
            >
              Wallet
            </button>
          );
        }}
      </ConnectKitButton.Custom>
    );
  };

  return (
    <div className="h-full overflow-y-auto bg-custom-gradient p-5 border-r border-gray-200">
      <div className="flex items-center justify-between mb-6">
        <button
          className="p-2 rounded-md text-gray-700 md:hidden"
          onClick={onClose}
          aria-label="Close menu"
        >
          <X size={20} />
        </button>
      </div>
      <nav className="space-y-2">
        {menuItems.map((item, index) => {
          const isActive = item.path && location.pathname === item.path;
          return (
            <div
              key={index}
              className={`flex items-center gap-3 px-4 py-2.5 rounded-lg cursor-pointer ${isActive ? 'bg-[#dae2f8] text-gray-700' : 'text-gray-700 hover:bg-[#dae2f8]'}`}
              onClick={() => handleItemClick(index, item.path)}
            >
              <item.icon size={20} className="flex-shrink-0" />
              {item.text === "Wallet" ? (
                <CustomConnectButton isActive={!!isActive} />
              ) : (
                <span className="text-sm">{item.text}</span>
              )}
            </div>
          );
        })}
      </nav>
    </div>
  );
};

export default Sidebar;
