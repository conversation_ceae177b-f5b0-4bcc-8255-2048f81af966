import React, { useState, useRef, useEffect } from 'react';
import { Search, CheckCircle, XCircle, Clock, AlertCircle, ExternalLink, ArrowRight } from 'lucide-react';
import { getExplorerTxUrl } from '../utils/networks';

interface TransactionStatus {
    exists: boolean;
    isDone?: boolean;
    network?: string;
    amount?: string;
    tokenAddress?: string;
    sender?: string;
    createdAt?: string;
    updatedAt?: string;
    message?: string;
}

interface TransactionEvents {
    hasBridgeEvents: boolean;
    events?: Array<{
        tokenAddress: string;
        sender: string;
        amount: string;
        blockNumber: number;
        transactionHash: string;
    }>;
    blockNumber?: number;
    status?: string;
    message?: string;
}

interface BridgeResult {
    success: boolean;
    message: string;
    queueData?: {
        txHash: string;
        tokenAddress: string;
        amount: string;
        sender: string;
        network: string;
    };
}

const TransactionHashPage = () => {
    const [txHash, setTxHash] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [status, setStatus] = useState<TransactionStatus | null>(null);
    const [events, setEvents] = useState<TransactionEvents | null>(null);
    const [bridgeResult, setBridgeResult] = useState<BridgeResult | null>(null);
    const [selectedNetwork, setSelectedNetwork] = useState<'BNB' | 'Bitroot'>('BNB');
    const [error, setError] = useState('');
    const [step, setStep] = useState<'input' | 'checking' | 'events' | 'bridge' | 'completed'>('input');
    const resultRef = useRef<HTMLDivElement>(null);

    const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || window.location.origin.replace(/:\d+$/, ':3001');

    const validateTxHash = (hash: string): boolean => {
        return /^0x[a-fA-F0-9]{64}$/.test(hash);
    };

    const checkTransactionStatus = async () => {
        if (!validateTxHash(txHash)) {
            setError('请输入有效的交易hash格式 (0x开头的64位十六进制字符)');
            return;
        }

        setIsLoading(true);
        setError('');
        setStep('checking');
        setStatus(null);
        setEvents(null);
        setBridgeResult(null);

        try {
            const response = await fetch(`${apiBaseUrl}/api/transaction/${txHash}/status`);
            const statusData: TransactionStatus = await response.json();

            if (!response.ok) {
                throw new Error(statusData.message || '检查交易状态失败');
            }

            setStatus(statusData);

            if (statusData.exists && statusData.isDone) {
                setStep('completed');
            } else {
                // 继续检查事件
                await checkTransactionEvents();
            }
        } catch (err) {
            setError(err instanceof Error ? err.message : '检查交易状态失败');
            setStep('input');
        } finally {
            setIsLoading(false);
        }
    };

    const checkTransactionEvents = async () => {
        setStep('events');
        
        try {
            const response = await fetch(`${apiBaseUrl}/api/transaction/${txHash}/events/${selectedNetwork}`);
            const eventsData: TransactionEvents = await response.json();

            if (!response.ok) {
                throw new Error(eventsData.message || '获取交易事件失败');
            }

            setEvents(eventsData);

            if (eventsData.hasBridgeEvents) {
                setStep('bridge');
            } else {
                setError('该交易不包含Bridge事件，无法进行跨链处理');
                setStep('input');
            }
        } catch (err) {
            setError(err instanceof Error ? err.message : '获取交易事件失败');
            setStep('input');
        }
    };

    const triggerBridge = async () => {
        if (!events || !events.hasBridgeEvents) {
            setError('没有有效的Bridge事件');
            return;
        }

        setIsLoading(true);
        setError('');

        try {
            const response = await fetch(`${apiBaseUrl}/api/transaction/${txHash}/bridge`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    network: selectedNetwork
                })
            });

            const result: BridgeResult = await response.json();

            if (!response.ok) {
                throw new Error(result.message || '触发跨链失败');
            }

            setBridgeResult(result);
            setStep('completed');
        } catch (err) {
            setError(err instanceof Error ? err.message : '触发跨链失败');
        } finally {
            setIsLoading(false);
        }
    };

    const resetForm = () => {
        setTxHash('');
        setStatus(null);
        setEvents(null);
        setBridgeResult(null);
        setError('');
        setStep('input');
    };

    const formatAmount = (amount: string): string => {
        try {
            // 假设amount是wei单位，转换为ether
            const ethValue = parseInt(amount, 16) / 1e18;
            return ethValue.toFixed(6);
        } catch {
            return amount;
        }
    };

    const formatAddress = (address: string): string => {
        if (!address) return '';
        return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;
    };

    const getNetworkIcon = (network: string) => {
        return network === 'BNB' ? './bnb.png' : './bitroot.svg';
    };

    const getExplorerUrl = (hash: string, network: string) => {
        const chainId = network === 'BNB' ? 97 : 80002; // BSC Testnet : Bitroot
        return getExplorerTxUrl(hash, chainId);
    };

    // 支持回车查询
    const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter') {
            checkTransactionStatus();
        }
    };

    // 查询后自动滚动到结果区域
    useEffect(() => {
        if (['checking', 'events', 'bridge', 'completed'].includes(step) && resultRef.current) {
            resultRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }, [step]);

    return (
        <div className="bg-[#d6a4a4] rounded-3xl shadow-sm border border-gray-200 p-6 w-full max-w-2xl mx-auto">
            <div className="flex flex-col h-full">
                <h1 className="text-2xl font-bold mb-6 text-center">交易Hash跨链查询</h1>
                
                {/* 步骤指示器 */}
                <div className="flex items-center justify-center mb-8">
                    <div className="flex items-center space-x-2">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                            ['input', 'checking', 'events', 'bridge', 'completed'].includes(step) 
                                ? 'bg-blue-500 text-white' : 'bg-gray-300'
                        }`}>
                            1
                        </div>
                        <div className="w-8 h-1 bg-gray-300">
                            <div className={`h-full bg-blue-500 transition-all duration-300 ${
                                ['checking', 'events', 'bridge', 'completed'].includes(step) ? 'w-full' : 'w-0'
                            }`}></div>
                        </div>
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                            ['events', 'bridge', 'completed'].includes(step) 
                                ? 'bg-blue-500 text-white' : 'bg-gray-300'
                        }`}>
                            2
                        </div>
                        <div className="w-8 h-1 bg-gray-300">
                            <div className={`h-full bg-blue-500 transition-all duration-300 ${
                                ['bridge', 'completed'].includes(step) ? 'w-full' : 'w-0'
                            }`}></div>
                        </div>
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                            step === 'completed' ? 'bg-green-500 text-white' : 'bg-gray-300'
                        }`}>
                            3
                        </div>
                    </div>
                </div>

                {/* 输入区域 */}
                <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        交易Hash
                    </label>
                    <div className="flex gap-2">
                        <input
                            type="text"
                            value={txHash}
                            onChange={(e) => setTxHash(e.target.value)}
                            onKeyDown={handleInputKeyDown}
                            placeholder="输入交易hash (0x...)"
                            className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            disabled={isLoading}
                        />
                        <select
                            value={selectedNetwork}
                            onChange={(e) => setSelectedNetwork(e.target.value as 'BNB' | 'Bitroot')}
                            className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            disabled={isLoading}
                        >
                            <option value="BNB">BNB Chain</option>
                            <option value="Bitroot">Bitroot Network</option>
                        </select>
                    </div>
                </div>

                {/* 搜索按钮 */}
                {step === 'input' && (
                    <button
                        onClick={checkTransactionStatus}
                        disabled={isLoading || !txHash}
                        className="w-full bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white font-medium py-3 px-4 rounded-lg flex items-center justify-center gap-2 transition-colors"
                    >
                        {isLoading ? (
                            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                        ) : (
                            <Search size={20} />
                        )}
                        {isLoading ? '检查中...' : '检查交易状态'}
                    </button>
                )}

                {/* 错误消息 */}
                {error && (
                    <div className="mt-4 p-4 bg-red-100 border border-red-300 rounded-lg flex items-center gap-2">
                        <AlertCircle size={20} className="text-red-500" />
                        <span className="text-red-700">{error}</span>
                    </div>
                )}

                {/* 结果区域 */}
                <div ref={resultRef} />

                {/* 交易状态显示 */}
                {status && (
                    <div className="mt-6 p-4 bg-white rounded-lg border border-gray-200">
                        <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                            {status.exists ? (
                                status.isDone ? (
                                    <CheckCircle className="text-green-500" size={24} />
                                ) : (
                                    <Clock className="text-yellow-500" size={24} />
                                )
                            ) : (
                                <XCircle className="text-gray-500" size={24} />
                            )}
                            跨链状态
                        </h3>
                        
                        {status.exists ? (
                            <div className="space-y-2">
                                <div className="flex justify-between">
                                    <span className="text-gray-600">状态:</span>
                                    <span className={`font-medium ${status.isDone ? 'text-green-600' : 'text-yellow-600'}`}>
                                        {status.isDone ? '已完成跨链' : '等待跨链处理'}
                                    </span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600">网络:</span>
                                    <div className="flex items-center gap-2">
                                        <img src={getNetworkIcon(status.network!)} alt={status.network} className="w-5 h-5" />
                                        <span>{status.network}</span>
                                    </div>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600">金额:</span>
                                    <span>{formatAmount(status.amount!)}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600">发送者:</span>
                                    <span className="font-mono">{formatAddress(status.sender!)}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600">创建时间:</span>
                                    <span>{new Date(status.createdAt!).toLocaleString()}</span>
                                </div>
                                {status.isDone && (
                                    <div className="mt-4 p-3 bg-green-50 rounded-lg">
                                        <p className="text-green-700 text-center font-medium">
                                            ✅ 该交易已经完成跨链处理
                                        </p>
                                    </div>
                                )}
                            </div>
                        ) : (
                            <p className="text-gray-600">该交易尚未处理过跨链</p>
                        )}
                    </div>
                )}

                {/* 事件信息显示 */}
                {events && (
                    <div className="mt-6 p-4 bg-white rounded-lg border border-gray-200">
                        <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                            <CheckCircle className="text-green-500" size={24} />
                            Bridge事件详情
                        </h3>
                        
                        {events.hasBridgeEvents ? (
                            <div className="space-y-4">
                                {events.events?.map((event, index) => (
                                    <div key={index} className="border border-gray-200 rounded-lg p-3">
                                        <div className="grid grid-cols-2 gap-2 text-sm">
                                            <div>
                                                <span className="text-gray-600">代币地址:</span>
                                                <p className="font-mono">{formatAddress(event.tokenAddress)}</p>
                                            </div>
                                            <div>
                                                <span className="text-gray-600">发送者:</span>
                                                <p className="font-mono">{formatAddress(event.sender)}</p>
                                            </div>
                                            <div>
                                                <span className="text-gray-600">金额:</span>
                                                <p>{formatAmount(event.amount)}</p>
                                            </div>
                                            <div>
                                                <span className="text-gray-600">区块号:</span>
                                                <p>{event.blockNumber}</p>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                                
                                <div className="flex justify-center">
                                    <a
                                        href={getExplorerUrl(txHash, selectedNetwork)}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 text-sm"
                                    >
                                        在区块链浏览器中查看 <ExternalLink size={16} />
                                    </a>
                                </div>
                            </div>
                        ) : (
                            <p className="text-gray-600">{events.message}</p>
                        )}
                    </div>
                )}

                {/* 跨链处理按钮 */}
                {step === 'bridge' && events?.hasBridgeEvents && (
                    <div className="mt-6">
                        <button
                            onClick={triggerBridge}
                            disabled={isLoading}
                            className="w-full bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white font-medium py-3 px-4 rounded-lg flex items-center justify-center gap-2 transition-colors"
                        >
                            {isLoading ? (
                                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                            ) : (
                                <ArrowRight size={20} />
                            )}
                            {isLoading ? '处理中...' : '开始跨链处理'}
                        </button>
                    </div>
                )}

                {/* 完成状态 */}
                {bridgeResult && (
                    <div className="mt-6 p-4 bg-green-100 border border-green-300 rounded-lg">
                        <div className="flex items-center gap-2 mb-3">
                            <CheckCircle className="text-green-500" size={24} />
                            <h3 className="text-lg font-semibold text-green-700">跨链处理完成</h3>
                        </div>
                        <p className="text-green-700 mb-4">{bridgeResult.message}</p>
                        
                        {bridgeResult.queueData && (
                            <div className="space-y-2 text-sm">
                                <div className="flex justify-between">
                                    <span className="text-gray-600">目标网络:</span>
                                    <span>{bridgeResult.queueData.network === 'BNB' ? 'Bitroot' : 'BNB'}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600">处理金额:</span>
                                    <span>{formatAmount(bridgeResult.queueData.amount)}</span>
                                </div>
                            </div>
                        )}
                        
                        <div className="mt-4 text-center">
                            <button
                                onClick={resetForm}
                                className="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                            >
                                处理新交易
                            </button>
                        </div>
                    </div>
                )}

                {/* 已完成状态（数据库中已存在且完成） */}
                {step === 'completed' && status?.isDone && (
                    <div className="mt-6 text-center">
                        <button
                            onClick={resetForm}
                            className="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                        >
                            查询其他交易
                        </button>
                    </div>
                )}
            </div>
        </div>
    );
};

export default TransactionHashPage; 