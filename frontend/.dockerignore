# 依赖
node_modules
.pnp
.pnp.js

# 生产构建 - 注释掉 dist，因为我们需要它
# dist
build

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 目录
.git
.github
.vscode
.idea

# 缓存
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz

# 测试
coverage
*.lcov

# 其他
*.tgz
*.tar.gz
.cache
.DS_Store
Thumbs.db
README.md
*.md
.dockerignore
Dockerfile*

# 开发工具
.eslintcache 