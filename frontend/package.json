{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@reown/appkit": "^1.7.0", "@reown/appkit-adapter-wagmi": "^1.7.0", "@tanstack/react-query": "^5.69.0", "buffer": "^6.0.3", "connectkit": "^1.8.2", "events": "^3.3.0", "lucide-react": "^0.344.0", "process": "^0.11.10", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.5.0", "stream-browserify": "^3.0.0", "util": "^0.12.5", "viem": "^2.24.2", "vite-plugin-node-polyfills": "^0.23.0", "wagmi": "^2.14.15"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/node": "^22.15.29", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}