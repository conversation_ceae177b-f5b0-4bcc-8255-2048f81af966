---
description: 
globs: 
alwaysApply: false
---
# Cursor AI 跨链桥项目规则

您是一位专业的全栈区块链开发人员，正在开发一个复杂的跨链桥项目。该项目包含多个组件：

## 项目结构
1. **智能合约** (`/contract/`) - 使用Foundry的Solidity合约
2. **后端服务** (`/indexer/`) - 使用Prisma ORM的TypeScript/Node.js
3. **前端应用** (`/frontend`) - React/Next.js应用程序

## 核心技术与标准

### 智能合约
- **框架**: Foundry用于开发、测试和部署
- **语言**: Solidity ^0.8.0
- **核心合约**:
  - `BridgeContract.sol`: 使用交易哈希去重的跨链桥
  - `EtherERC20.sol`: 带有附加功能的自定义ERC20代币
- **安全性**: 使用基于交易哈希的去重，而非基于nonce的系统
- **测试**: 使用Forge编写全面的测试
- **脚本**: 使用Foundry脚本进行部署和管理

### 后端服务
- **语言**: TypeScript与Node.js
- **数据库**: PostgreSQL与Prisma ORM
- **队列**: Redis与Bull队列用于交易处理
- **架构**: 模块化设计，包含独立的管理器：
  - 安全验证（交易哈希去重）
  - 数据库操作
  - 网络提供商（Web3/Ethers.js）
  - 交易扫描和处理
  - 队列管理

### 核心架构原则
1. **基于交易哈希的去重**: 始终使用`txHash`作为唯一标识，而非nonce
2. **模块化设计**: 将关注点分离到专用的管理器类中
3. **错误处理**: 全面的错误分类和重试逻辑
4. **安全优先**: 在处理前验证所有交易
5. **可扩展性**: 使用基于队列的处理实现高吞吐量

## 编码标准

### 通用规则
- 在被要求时始终用**英文注释**编写代码
- 使用清晰、描述性的变量和函数名
- 实现全面的错误处理
- 添加详细的日志用于调试和监控
- 为所有关键功能编写测试

### 智能合约指导原则
- 尽可能使用OpenZeppelin合约作为基础
- 实现适当的访问控制（Ownable等）
- 添加全面的事件用于链下监控
- 使用自定义错误而非字符串回滚以提高gas效率
- 验证所有输入并检查外部调用结果
- 示例模式：
```solidity
contract BridgeContract is Ownable {
    error BridgeContract__Transaction_Already_Processed();
    
    mapping(bytes32 => bool) public processedTransactions;
    
    function redeem(IERC20 _token, address _to, uint256 _amount, bytes32 _txHash) external onlyOwner {
        if (processedTransactions[_txHash]) {
            revert BridgeContract__Transaction_Already_Processed();
        }
        // 处理交易...
        processedTransactions[_txHash] = true;
    }
}
```

### TypeScript后端指导原则
- 使用严格的TypeScript配置
- 为所有数据结构实现适当的类型定义
- 对管理器类使用依赖注入模式
- 对配置管理实现单例模式
- 示例模式：
```typescript
class SecurityManager {
    public async validateTransactionSecurity(
        network: NetworkName,
        txHash: string,
        blockNumber: number
    ): Promise<{ isValid: boolean; reason?: string }> {
        // 使用txHash而非nonce的验证逻辑
    }
}
```

### 数据库模式
- 在交易表中使用`txHash`作为唯一标识
- 包含网络信息以支持多链
- 使用`isDone`布尔值跟踪处理状态
- 示例模式：
```prisma
model TransactionData {
  id           Int      @id @default(autoincrement())
  txHash       String   @unique
  isDone       Boolean  @default(false)
  tokenAddress String
  amount       String
  sender       String
  network      String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
}
```

## 文件组织与模式

### 智能合约 (`/contract/`)
```
contract/
├── src/
│   ├── BridgeContract.sol      # 主桥接合约
│   └── EtherERC20.sol         # 自定义ERC20代币
├── script/
│   ├── Bridge.s.sol           # 部署脚本
│   ├── TokenTransfer.s.sol    # 转账管理
│   └── token_transfer.sh      # 辅助shell脚本
└── test/                      # 全面测试
```

### 后端服务 (`/indexer/`)
```
indexer/
├── src/
│   ├── types.ts              # 类型定义
│   ├── config.ts             # 配置管理
│   ├── security.ts           # 安全验证
│   ├── database.ts           # 数据库操作
│   ├── scanner.ts            # 区块链扫描
│   ├── queue.ts              # 队列管理
│   ├── transfer.ts           # 跨链转账
│   └── index.ts              # 主应用程序
├── prisma/
│   └── schema.prisma         # 数据库模式
└── package.json
```

## 开发工作流

### 处理智能合约时
1. 以安全优先的思维编写合约
2. 创建全面的测试套件
3. 使用部署脚本确保一致的部署
4. 在区块浏览器上验证合约
5. 彻底记录所有公共函数

### 处理后端时
1. 从`types.ts`中的类型定义开始
2. 在专用管理器中实现业务逻辑
3. 如需要添加数据库模型
4. 为关键路径编写测试
5. 添加监控和日志

### 处理前端时
1. 使用TypeScript确保类型安全
2. 实现适当的错误边界
3. 为异步操作添加加载状态
4. 彻底验证用户输入
5. 适当使用Web3库

## 安全考虑
- **交易验证**: 始终使用`txHash`唯一性验证交易
- **输入清理**: 验证所有用户输入和外部数据
- **访问控制**: 为敏感操作实现适当的权限
- **速率限制**: 为API端点实现速率限制
- **私钥管理**: 永远不要记录或暴露私钥
- **Gas优化**: 优化合约的gas效率

## 需要遵循的通用模式

### 错误处理模式
```typescript
try {
    const result = await riskyOperation();
    return { success: true, data: result };
} catch (error) {
    const errorInfo = this.categorizeError(error, context);
    console.error(`操作失败: ${errorInfo.category}`, errorInfo);
    return { success: false, error: errorInfo };
}
```

### 配置管理模式
```typescript
const ConfigManager = (() => {
    let instance: ConfigManager | null = null;
    return {
        getInstance: (): ConfigManager => {
            if (!instance) instance = new ConfigManager();
            return instance;
        }
    };
})();
```

### 数据库操作模式
```typescript
public async processTransaction(txHash: string): Promise<void> {
    const existingTx = await this.prisma.transactionData.findUnique({
        where: { txHash }
    });
    
    if (existingTx?.isDone) {
        throw new Error('交易已经被处理');
    }
    
    // 处理交易...
}
```

## 进行更改时
1. **合约**: 始终考虑安全影响和gas成本
2. **后端**: 确保数据库迁移安全且向后兼容
3. **前端**: 测试所有用户流程和错误状态
4. **集成**: 测试所有组件的端到端流程

## 性能优化
- 对多个交易使用批处理操作
- 实现适当的缓存策略
- 通过适当的索引优化数据库查询
- 对重处理使用队列系统
- 监控和分析关键路径

不要每次修改完代码都写一个文档，就在对话框中做总结即可

记住：这是一个处理真实资产的金融应用程序。安全性、可靠性和正确性至关重要。始终谨慎行事，实现全面的测试和验证。
