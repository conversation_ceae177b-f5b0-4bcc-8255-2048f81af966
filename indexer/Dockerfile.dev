# 开发版本 Dockerfile for Indexer
FROM node:20-alpine as development

# 设置工作目录
WORKDIR /app

# 安装必要的系统依赖
RUN apk add --no-cache openssl git postgresql-client

# 复制package.json和yarn.lock
COPY package.json yarn.lock ./

# 安装所有依赖（包括开发依赖）
RUN npm install --omit=dev

# 复制prisma配置
COPY prisma/ ./prisma/

# 生成Prisma客户端
RUN npx prisma generate

# 复制源代码
COPY . .

# 暴露端口（如果需要）
EXPOSE 3001

# 开发模式启动命令
CMD ["yarn", "dev"] 