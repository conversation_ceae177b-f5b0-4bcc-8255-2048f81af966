import express from 'express';
import cors from 'cors';
import { PrismaClient } from "@prisma/client";
import { NetworkName, BridgeJobData } from './types';
import { ProviderManager } from './providers';
import SecurityManager from './security';
import DatabaseQueueManager from './databaseQueue';
import { BRIDGE_EVENT_SIGNATURE } from './constants';

class ApiServer {
    private app: express.Application;
    private prisma: PrismaClient;
    private providerManager: ProviderManager;
    private securityManager: SecurityManager;
    private bridgeQueue: DatabaseQueueManager;

    constructor(
        prisma: PrismaClient,
        providerManager: ProviderManager,
        securityManager: SecurityManager,
        bridgeQueue: DatabaseQueueManager
    ) {
        this.app = express();
        this.prisma = prisma;
        this.providerManager = providerManager;
        this.securityManager = securityManager;
        this.bridgeQueue = bridgeQueue;
        
        this.setupMiddleware();
        this.setupRoutes();
    }

    private setupMiddleware(): void {
        this.app.use(cors());
        this.app.use(express.json());
        this.app.use(express.urlencoded({ extended: true }));
    }

    private setupRoutes(): void {
        // 健康检查
        this.app.get('/api/health', (req, res) => {
            res.json({ status: 'ok', timestamp: new Date().toISOString() });
        });

        // 检查交易是否已处理过跨链
        this.app.get('/api/transaction/:txHash/status', async (req, res) => {
            try {
                const { txHash } = req.params;
                
                if (!txHash || !txHash.startsWith('0x')) {
                    return res.status(400).json({
                        error: '无效的交易hash格式'
                    });
                }

                const transaction = await this.prisma.transactionData.findUnique({
                    where: { txHash }
                });

                if (transaction) {
                    return res.json({
                        exists: true,
                        isDone: transaction.isDone,
                        network: transaction.network,
                        amount: transaction.amount,
                        tokenAddress: transaction.tokenAddress,
                        sender: transaction.sender,
                        createdAt: transaction.createdAt,
                        updatedAt: transaction.updatedAt
                    });
                } else {
                    return res.json({
                        exists: false,
                        message: '该交易尚未处理过跨链'
                    });
                }
            } catch (error) {
                console.error('检查交易状态失败:', error);
                res.status(500).json({
                    error: '服务器内部错误'
                });
            }
        });

        // 获取交易的事件信息
        this.app.get('/api/transaction/:txHash/events/:network', async (req, res) => {
            try {
                const { txHash, network } = req.params;
                
                if (!txHash || !txHash.startsWith('0x')) {
                    return res.status(400).json({
                        error: '无效的交易hash格式'
                    });
                }

                if (!['BNB', 'Bitroot'].includes(network)) {
                    return res.status(400).json({
                        error: '不支持的网络类型'
                    });
                }

                const provider = this.providerManager.getProvider(network as NetworkName);
                const contract = this.providerManager.getContract(network as NetworkName);

                if (!provider || !contract) {
                    return res.status(500).json({
                        error: `${network} 网络提供者或合约未连接`
                    });
                }

                // 获取交易回执
                const receipt = await provider.getTransactionReceipt(txHash);
                if (!receipt) {
                    return res.status(404).json({
                        error: '未找到该交易'
                    });
                }

                // 获取Bridge事件
                const bridgeEventSignature = BRIDGE_EVENT_SIGNATURE;
                
                const bridgeEvents = receipt.logs.filter(log => 
                    log.address.toLowerCase() === (contract.target as string).toLowerCase() &&
                    log.topics[0] === bridgeEventSignature
                );

                if (bridgeEvents.length === 0) {
                    return res.json({
                        hasBridgeEvents: false,
                        message: '该交易不包含Bridge事件'
                    });
                }

                // 解析事件数据
                const events = bridgeEvents.map(log => ({
                    tokenAddress: "0x" + log.topics[1].substring(26),
                    sender: "0x" + log.topics[2].substring(26),
                    amount: log.data,
                    blockNumber: receipt.blockNumber,
                    transactionHash: receipt.hash
                }));

                return res.json({
                    hasBridgeEvents: true,
                    events,
                    blockNumber: receipt.blockNumber,
                    status: receipt.status
                });

            } catch (error) {
                console.error('获取交易事件失败:', error);
                res.status(500).json({
                    error: '获取交易事件失败'
                });
            }
        });

        // 手动触发跨链交易
        this.app.post('/api/transaction/:txHash/bridge', async (req, res) => {
            try {
                const { txHash } = req.params;
                const { network } = req.body;

                if (!txHash || !txHash.startsWith('0x')) {
                    return res.status(400).json({
                        error: '无效的交易hash格式'
                    });
                }

                if (!['BNB', 'Bitroot'].includes(network)) {
                    return res.status(400).json({
                        error: '不支持的网络类型'
                    });
                }

                // 先检查是否已经处理过
                const existingTransaction = await this.prisma.transactionData.findUnique({
                    where: { txHash }
                });

                if (existingTransaction && existingTransaction.isDone) {
                    return res.status(400).json({
                        error: '该交易已经完成跨链处理'
                    });
                }

                // 获取交易事件
                const eventsResponse = await this.getTransactionEvents(txHash, network as NetworkName);
                
                if (!eventsResponse.hasBridgeEvents) {
                    return res.status(400).json({
                        error: '该交易不包含有效的Bridge事件'
                    });
                }

                const event = eventsResponse.events[0]; // 取第一个事件

                // 验证交易安全性
                const provider = this.providerManager.getProvider(network as NetworkName);
                const currentBlock = await provider.getBlockNumber();
                const securityResult = await this.securityManager.validateTransactionSecurity(
                    network as NetworkName,
                    txHash,
                    event.blockNumber,
                    currentBlock
                );

                if (!securityResult.isValid) {
                    return res.status(400).json({
                        error: `交易安全验证失败: ${securityResult.reason}`
                    });
                }

                // 添加到处理队列
                await this.bridgeQueue.addJob({
                    txHash: txHash,
                    tokenAddress: event.tokenAddress,
                    amount: event.amount,
                    sender: event.sender,
                    network: network as NetworkName,
                    isRescan: true
                });

                console.log(`✅ 手动添加交易 ${txHash} 到处理队列`);

                res.json({
                    success: true,
                    message: '交易已添加到跨链处理队列',
                    queueData: {
                        txHash,
                        tokenAddress: event.tokenAddress,
                        amount: event.amount,
                        sender: event.sender,
                        network
                    }
                });

            } catch (error) {
                console.error('手动触发跨链失败:', error);
                res.status(500).json({
                    error: '手动触发跨链失败'
                });
            }
        });

        // 获取队列状态
        this.app.get('/api/queue/status', async (req, res) => {
            try {
                const stats = await this.bridgeQueue.getQueueStats();

                res.json({
                    waiting: stats.pending,
                    active: stats.processing,
                    completed: stats.completed,
                    failed: stats.failed,
                    timestamp: new Date().toISOString()
                });
            } catch (error) {
                console.error('获取队列状态失败:', error);
                res.status(500).json({
                    error: '获取队列状态失败'
                });
            }
        });
    }

    private async getTransactionEvents(txHash: string, network: NetworkName): Promise<any> {
        const provider = this.providerManager.getProvider(network);
        const contract = this.providerManager.getContract(network);

        if (!provider || !contract) {
            throw new Error(`${network} 网络提供者或合约未连接`);
        }

        const receipt = await provider.getTransactionReceipt(txHash);
        if (!receipt) {
            throw new Error('未找到该交易');
        }

        const bridgeEventSignature = BRIDGE_EVENT_SIGNATURE;
        
        const bridgeEvents = receipt.logs.filter(log => 
            log.address.toLowerCase() === (contract.target as string).toLowerCase() &&
            log.topics[0] === bridgeEventSignature
        );

        if (bridgeEvents.length === 0) {
            return {
                hasBridgeEvents: false,
                message: '该交易不包含Bridge事件'
            };
        }

        const events = bridgeEvents.map(log => ({
            tokenAddress: "0x" + log.topics[1].substring(26),
            sender: "0x" + log.topics[2].substring(26),
            amount: log.data,
            blockNumber: receipt.blockNumber,
            transactionHash: receipt.hash
        }));

        return {
            hasBridgeEvents: true,
            events,
            blockNumber: receipt.blockNumber,
            status: receipt.status
        };
    }

    public start(port: number = 3001): void {
        this.app.listen(port, () => {
            console.log(`🌐 API服务器已启动，端口: ${port}`);
            console.log(`📋 API端点:`);
            console.log(`  GET  /api/health`);
            console.log(`  GET  /api/transaction/:txHash/status`);
            console.log(`  GET  /api/transaction/:txHash/events/:network`);
            console.log(`  POST /api/transaction/:txHash/bridge`);
            console.log(`  GET  /api/queue/status`);
        });
    }

    public getApp(): express.Application {
        return this.app;
    }
}

export default ApiServer; 