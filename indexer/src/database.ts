import { PrismaClient } from "@prisma/client";
import { NetworkName } from './types';

class DatabaseManager {
    private prisma: PrismaClient;

    constructor() {
        this.prisma = new PrismaClient();
    }

    public getPrismaClient(): PrismaClient {
        return this.prisma;
    }

    /**
     * 初始化数据库连接和schema
     */
    public async initializeDatabase(): Promise<void> {
        console.log("🔌 Initializing database connection...");
        
        const maxRetries = 30; // 最多重试30次
        const retryInterval = 1000; // 每次间隔2秒
        
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                // 测试数据库连接
                await this.prisma.$connect();
                console.log("✅ Database connected successfully");

                // 测试基本查询
                await this.testDatabaseConnection();
                return; // 成功连接，退出重试循环
                
            } catch (error) {
                console.log(`🔄 Database connection attempt ${attempt}/${maxRetries} failed`);
                
                if (error.code === 'P1001') {
                    if (attempt === maxRetries) {
                        console.error("❌ Can't reach database server after all retries. Please check your database configuration.");
                        console.log("💡 提示: 请确保 DATABASE_URL 环境变量正确配置，并且数据库服务器正在运行");
                        throw error;
                    } else {
                        console.log(`⏳ Waiting ${retryInterval/1000}s before retry...`);
                        await this.sleep(retryInterval);
                        continue;
                    }
                } else if (error.code === 'P2021' || error.message.includes('does not exist')) {
                    console.log("📋 Database table does not exist, creating schema...");
                    await this.createDatabaseSchema();
                    return;
                } else if (error.code === 'P3009' || error.message.includes('P3009')) {
                    console.log("📋 Database schema might not exist, attempting to create...");
                    await this.createDatabaseSchema();
                    return;
                } else {
                    throw error;
                }
            }
        }
    }

    private sleep(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    private async testDatabaseConnection(): Promise<void> {
        const networks = await this.prisma.networkStatus.findMany();
        console.log(`📊 Found ${networks.length} network status records`);

        const transactions = await this.prisma.transactionData.count();
        console.log(`📊 Found ${transactions} transaction records`);
    }

    private async createDatabaseSchema(): Promise<void> {
        const { execSync } = await import('child_process');
        
        try {
            console.log("🔧 Running 'prisma db push' to create database schema...");
            execSync('npx prisma db push --skip-generate', {
                stdio: 'inherit',
                cwd: process.cwd()
            });

            console.log("✅ Database schema created successfully!");
            await this.testDatabaseConnection();
            console.log("✅ Database initialization completed successfully");
        } catch (pushError) {
            console.error("❌ Failed to create database schema:", pushError);
            console.error("请检查数据库配置和权限");
            throw pushError;
        }
    }

    /**
     * 获取网络状态统计信息
     */
    public async getNetworkStats() {
        try {
            const stats = await Promise.all([
                this.prisma.networkStatus.findMany(),
                this.prisma.transactionData.groupBy({
                    by: ['network'],
                    _count: {
                        _all: true
                    }
                }),
                this.prisma.transactionData.groupBy({
                    by: ['network', 'isDone'],
                    _count: {
                        _all: true
                    }
                })
            ]);

            const [networkStatus, transactionsByNetwork, transactionsByStatus] = stats;

            return {
                networkStatus,
                transactionsByNetwork,
                transactionsByStatus
            };
        } catch (error) {
            console.error("❌ 获取网络统计信息失败:", error);
            return null;
        }
    }

    /**
     * 清理旧的交易记录（可选功能）
     * @param daysOld 保留多少天的记录
     */
    public async cleanupOldTransactions(daysOld: number = 30): Promise<number> {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysOld);

            const result = await this.prisma.transactionData.deleteMany({
                where: {
                    AND: [
                        { isDone: true },
                        { 
                            createdAt: { lt: cutoffDate }
                        }
                    ]
                }
            });

            console.log(`🧹 Cleaned up ${result.count} old transaction records`);
            return result.count;
        } catch (error) {
            console.error("❌ 清理旧交易记录失败:", error);
            return 0;
        }
    }

    /**
     * 获取数据库健康状态
     */
    public async getHealthStatus(): Promise<{
        isHealthy: boolean;
        latency: number;
        error?: string;
    }> {
        const startTime = Date.now();
        
        try {
            // 执行一个简单的查询来测试连接
            await this.prisma.$queryRaw`SELECT 1`;
            
            const latency = Date.now() - startTime;
            
            return {
                isHealthy: true,
                latency
            };
        } catch (error) {
            return {
                isHealthy: false,
                latency: Date.now() - startTime,
                error: error.message
            };
        }
    }

    /**
     * 备份关键数据（导出为JSON）
     */
    public async exportData(): Promise<{
        networkStatus: any[];
        pendingTransactions: any[];
    }> {
        try {
            const [networkStatus, pendingTransactions] = await Promise.all([
                this.prisma.networkStatus.findMany(),
                this.prisma.transactionData.findMany({
                    where: { isDone: false }
                })
            ]);

            console.log("📦 Data exported successfully");
            return {
                networkStatus,
                pendingTransactions
            };
        } catch (error) {
            console.error("❌ 数据导出失败:", error);
            throw error;
        }
    }

    /**
     * 重置网络状态（谨慎使用）
     * @param network 要重置的网络，如果不指定则重置所有网络
     */
    public async resetNetworkStatus(network?: NetworkName): Promise<void> {
        try {
            if (network) {
                await this.prisma.networkStatus.deleteMany({
                    where: { network }
                });
                console.log(`🔄 Reset network status for ${network}`);
            } else {
                await this.prisma.networkStatus.deleteMany();
                console.log("🔄 Reset all network status");
            }
        } catch (error) {
            console.error("❌ 重置网络状态失败:", error);
            throw error;
        }
    }

    public async disconnect(): Promise<void> {
        try {
            await this.prisma.$disconnect();
            console.log("✅ Database disconnected successfully");
        } catch (error) {
            console.error("❌ Error disconnecting from database:", error);
        }
    }

    /**
     * 记录数据库操作日志
     */
    public logDatabaseOperation(operation: string, details?: any): void {
        console.log(`💾 Database operation: ${operation}`);
        if (details) {
            console.log(`📋 Details:`, details);
        }
    }
}

export default DatabaseManager; 