import { NetworkName, BridgeJobData, ScanRange } from './types';
import { PrismaClient } from "@prisma/client";
import { ProviderManager } from './providers';
import SecurityManager from './security';
import ConfigManager from './config';
import DatabaseQueueManager from './databaseQueue';
import { BRIDGE_EVENT_SIGNATURE } from './constants';

class Scanner {
    private prisma: PrismaClient;
    private providerManager: ProviderManager;
    private securityManager: SecurityManager;
    private bridgeQueue: DatabaseQueueManager;
    private config = ConfigManager.getInstance();

    constructor(
        prisma: PrismaClient,
        providerManager: ProviderManager,
        securityManager: SecurityManager,
        bridgeQueue: DatabaseQueueManager
    ) {
        this.prisma = prisma;
        this.providerManager = providerManager;
        this.securityManager = securityManager;
        this.bridgeQueue = bridgeQueue;
    }

    /**
     * 启动时重新扫描遗漏的交易
     * @param network 网络名称
     * @param isFirstRun 是否是第一次运行
     */
    public async performStartupRescan(network: NetworkName, isFirstRun: boolean = false): Promise<void> {
        console.log(`\n=== 🔍 ${network} 网络启动扫描检查 ===`);

        try {
            const provider = this.providerManager.getProvider(network);
            const contract = this.providerManager.getContract(network);

            if (!provider || !contract) {
                console.error(`❌ ${network} 网络提供者或合约未连接，跳过启动扫描`);
                return;
            }

            const scanRange = await this.determineScanRange(network, provider, isFirstRun);
            if (!scanRange) {
                console.log(`✅ ${network} 网络无需扫描`);
                return;
            }

            const { scanFromBlock, scanToBlock } = scanRange;
            await this.scanEventsWithRateLimit(network, provider, contract, scanFromBlock, scanToBlock);

        } catch (error) {
            console.error(`❌ ${network} 网络启动扫描失败:`, error);


        }
    }

    private async determineScanRange(network: NetworkName, provider: any, isFirstRun: boolean = false): Promise<ScanRange | null> {
        const latestBlock = await provider.getBlockNumber();
        console.log(`📊 ${network} 网络当前最新区块: ${latestBlock}`);
        console.log(`🔄 扫描模式: ${isFirstRun ? '首次运行' : '定期扫描'}`);

        let scanFromBlock: number;

        if (isFirstRun) {
            // 第一次运行：优先使用配置的重新扫描起始高度
            const configRescanBlock = this.config.config.RESCAN_FROM_BLOCK[network];
            if (configRescanBlock !== null) {
                scanFromBlock = configRescanBlock;
                console.log(`🎯 首次运行，使用配置的重新扫描起始区块: ${scanFromBlock}`);
            } else {
                // 如果没有配置，使用当前区块减去默认扫描范围
                scanFromBlock = Math.max(latestBlock - this.config.config.MAX_RESCAN_BLOCKS, 0);
                console.log(`🔄 首次运行但无配置，使用默认扫描范围，从区块 ${scanFromBlock} 开始`);
            }
        } else {
            // 定期扫描：优先使用数据库中的最后处理区块
            const lastProcessed = await this.prisma.networkStatus.findUnique({
                where: { network }
            });

            if (lastProcessed) {
                scanFromBlock = lastProcessed.lastProcessedBlock;
                console.log(`📋 定期扫描，使用数据库中的最后处理区块: ${scanFromBlock}`);
            } else {
                // 如果数据库中没有记录，使用配置的重新扫描起始高度
                const configRescanBlock = this.config.config.RESCAN_FROM_BLOCK[network];
                if (configRescanBlock !== null) {
                    scanFromBlock = configRescanBlock;
                    console.log(`🎯 数据库无记录，使用配置的重新扫描起始区块: ${scanFromBlock}`);
                } else {
                    // 如果都没有，使用当前区块减去默认扫描范围
                    scanFromBlock = Math.max(latestBlock - this.config.config.MAX_RESCAN_BLOCKS, 0);
                    console.log(`🔄 无任何配置，使用默认扫描范围，从区块 ${scanFromBlock} 开始`);
                }
            }
        }

        // 限制扫描范围，防止扫描太多历史数据
        const maxScanToBlock = scanFromBlock + this.config.config.MAX_RESCAN_BLOCKS;
        const scanToBlock = Math.min(latestBlock, maxScanToBlock);

        if (scanFromBlock >= scanToBlock) {
            console.log(`✅ ${network} 网络无需扫描，起始区块 ${scanFromBlock} >= 结束区块 ${scanToBlock}`);
            return null;
        }

        const scanRange = scanToBlock - scanFromBlock;
        console.log(`🔍 ${network} 网络扫描范围: ${scanFromBlock} - ${scanToBlock} (${scanRange} 个区块)`);

        if (scanRange > this.config.config.MAX_RESCAN_BLOCKS) {
            console.warn(`⚠️  扫描范围过大 (${scanRange} > ${this.config.config.MAX_RESCAN_BLOCKS})，已限制到最大范围`);
        }

        return { scanFromBlock, scanToBlock };
    }

    /**
     * 带rate limit处理的分批扫描
     */
    private async scanEventsWithRateLimit(
        network: NetworkName,
        provider: any,
        contract: any,
        fromBlock: number,
        toBlock: number
    ): Promise<void> {
        const rateLimitConfig = this.config.getRateLimitConfig();

        console.log(`📋 开始分批扫描 ${network} 网络历史事件...`);
        console.log(`🔍 总范围: ${fromBlock} - ${toBlock} (${toBlock - fromBlock} 个区块)`);
        console.log(`📦 每批最大区块数: ${rateLimitConfig.MAX_BLOCKS_PER_QUERY}`);

        let currentBlock = fromBlock;
        let totalProcessed = 0;
        let totalSkipped = 0;
        let batchCount = 0;

        while (currentBlock < toBlock) {
            const batchEnd = Math.min(currentBlock + rateLimitConfig.MAX_BLOCKS_PER_QUERY - 1, toBlock);
            batchCount++;

            console.log(`\n📦 批次 ${batchCount}: 扫描区块 ${currentBlock} - ${batchEnd}`);

            try {
                const { processed, skipped } = await this.scanEventsBatch(
                    network,
                    provider,
                    contract,
                    currentBlock,
                    batchEnd
                );

                totalProcessed += processed;
                totalSkipped += skipped;

                console.log(`✅ 批次 ${batchCount} 完成: 处理 ${processed}, 跳过 ${skipped}`);

            } catch (error) {
                if (this.isRateLimitError(error)) {
                    console.warn(`⚠️  批次 ${batchCount} 遇到rate limit，等待后重试...`);
                    await this.handleRateLimitError(network, provider, contract, currentBlock, batchEnd);
                } else {
                    console.error(`❌ 批次 ${batchCount} 扫描失败:`, error.message);
                    // 继续下一批次，避免整个扫描失败
                }
            }

            currentBlock = batchEnd + 1;

            // 批次间延迟，避免过快请求
            if (currentBlock < toBlock) {
                console.log(`⏳ 等待 ${rateLimitConfig.BATCH_DELAY_MS}ms 后继续下一批次...`);
                await this.delay(rateLimitConfig.BATCH_DELAY_MS);
            }
        }

        // 更新最后处理区块
        await this.updateLastProcessedBlock(network, toBlock, 0);

        // 记录总体结果
        this.logScanResults(network, totalProcessed + totalSkipped, totalProcessed, totalSkipped);
        console.log(`🎉 ${network} 网络分批扫描完成，共处理 ${batchCount} 个批次`);
    }

    /**
     * 扫描单个批次的事件
     */
    private async scanEventsBatch(
        network: NetworkName,
        provider: any,
        contract: any,
        fromBlock: number,
        toBlock: number
    ): Promise<{ processed: number; skipped: number }> {
        const bridgeEventSignature = BRIDGE_EVENT_SIGNATURE;

        let logs;
        
        // 对于Bitroot网络，获取所有事件然后手动过滤，避免RPC查询遗漏
        if (network === 'Bitroot') {
            console.log(`🌍 Bitroot网络检测到，使用全事件扫描模式`);
            
            // 获取所有事件
            const allLogs = await provider.getLogs({
                fromBlock,
                toBlock
            });
            
            // 手动过滤出目标合约的Bridge事件
            logs = allLogs.filter(log => 
                log.address.toLowerCase() === (contract.target as string).toLowerCase() &&
                log.topics[0] === bridgeEventSignature
            );
            
            console.log(`📋 批次扫描结果: 区块 ${fromBlock}-${toBlock}, 获取所有事件 ${allLogs.length} 个, 过滤出Bridge事件 ${logs.length} 个`);
        } else {
            // 其他网络使用原有的精确查询
            logs = await provider.getLogs({
                address: contract.target,
                topics: [bridgeEventSignature],
                fromBlock,
                toBlock
            });
            
            console.log(`📋 批次扫描结果: 区块 ${fromBlock}-${toBlock}, 发现 ${logs.length} 个Bridge事件`);
        }

        let processed = 0;
        let skipped = 0;

        for (const log of logs) {
            try {
                const txHash = log.transactionHash;
                console.log(`🔍 处理历史事件: ${txHash}`);

                // 验证交易安全性（包括重复检查）
                const currentBlock = await provider.getBlockNumber();
                const securityResult = await this.securityManager.validateTransactionSecurity(
                    network, 
                    txHash, 
                    log.blockNumber,
                    currentBlock
                );

                if (!securityResult.isValid) {
                    console.log(`⚠️  跳过交易 ${txHash}: ${securityResult.reason}`);
                    skipped++;
                    continue;
                }

                await this.processHistoricalEvent(log, network);
                processed++;

            } catch (error) {
                console.error(`❌ 处理历史事件失败:`, error);
                skipped++;
            }
        }

        return { processed, skipped };
    }

    /**
     * 处理rate limit错误的重试逻辑
     */
    private async handleRateLimitError(
        network: NetworkName,
        provider: any,
        contract: any,
        fromBlock: number,
        toBlock: number
    ): Promise<void> {
        const rateLimitConfig = this.config.getRateLimitConfig();

        for (let attempt = 1; attempt <= rateLimitConfig.MAX_RETRIES; attempt++) {
            const delay = rateLimitConfig.RETRY_DELAY_MS * Math.pow(2, attempt - 1);

            console.log(`🔄 第 ${attempt} 次重试，等待 ${delay}ms...`);
            await this.delay(delay);

            try {
                await this.scanEventsBatch(network, provider, contract, fromBlock, toBlock);
                console.log(`✅ 重试成功`);
                return;
            } catch (error) {
                if (this.isRateLimitError(error)) {
                    console.warn(`⚠️  重试 ${attempt} 仍遇到rate limit`);
                    if (attempt === rateLimitConfig.MAX_RETRIES) {
                        console.error(`❌ 达到最大重试次数，跳过区块范围 ${fromBlock}-${toBlock}`);
                        throw error;
                    }
                } else {
                    console.error(`❌ 重试遇到其他错误:`, error.message);
                    throw error;
                }
            }
        }
    }

    /**
     * 检查是否为rate limit错误
     */
    private isRateLimitError(error: any): boolean {
        const errorMessage = error.message?.toLowerCase() || '';
        const errorCode = error.code;

        return (
            errorMessage.includes('rate limit') ||
            errorMessage.includes('rate exceeded') ||
            errorMessage.includes('too many requests') ||
            errorCode === -32005 ||
            errorCode === 429
        );
    }

    /**
     * 延迟函数
     */
    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 扫描事件的原始方法（保留向后兼容）
     */
    private async scanEvents(
        network: NetworkName,
        provider: any,
        contract: any,
        fromBlock: number,
        toBlock: number
    ): Promise<void> {
        const rateLimitConfig = this.config.getRateLimitConfig();

        // 检查区块范围是否太大，如果是则使用分批扫描
        const blockRange = toBlock - fromBlock;
        if (blockRange > rateLimitConfig.MAX_BLOCKS_PER_QUERY) {
            console.log(`📦 区块范围过大 (${blockRange})，使用分批扫描...`);
            await this.scanEventsWithRateLimit(network, provider, contract, fromBlock, toBlock);
            return;
        }

        const bridgeEventSignature = BRIDGE_EVENT_SIGNATURE;

        console.log(`📋 开始扫描 ${network} 网络历史事件...`);
        console.log(`🔍 事件签名: ${bridgeEventSignature}`);
        console.log(`📍 合约地址: ${contract.target}`);

        let logs;
        
        // 对于Bitroot网络，获取所有事件然后手动过滤，避免RPC查询遗漏
        if (network === 'Bitroot') {
            console.log(`🌍 Bitroot网络检测到，使用全事件扫描模式`);
            
            // 获取所有事件
            const allLogs = await provider.getLogs({
                fromBlock,
                toBlock,
            });
            
            // 手动过滤出目标合约的Bridge事件
            logs = allLogs.filter(log => 
                log.address.toLowerCase() === (contract.target as string).toLowerCase() &&
                log.topics[0] === bridgeEventSignature
            );
            
            console.log(`📝 在 ${network} 网络获取所有事件 ${allLogs.length} 个，过滤出 ${logs.length} 个Bridge历史事件`);
        } else {
            // 其他网络使用原有的精确查询
            logs = await provider.getLogs({
                address: contract.target as string,
                topics: [bridgeEventSignature],
                fromBlock,
                toBlock,
            });
            
            console.log(`📝 在 ${network} 网络找到 ${logs.length} 个历史事件`);
        }

        if (logs.length === 0) {
            console.log(`⚠️  在指定区块范围内没有找到Bridge事件`);
            console.log(`这可能意味着：`);
            console.log(`  1. 交易还没有被确认`);
            console.log(`  2. 交易不在查询的区块范围内`);
            console.log(`  3. 合约地址不正确`);
            console.log(`  4. 事件签名不正确`);

            // 即使没有事件，也更新最后处理区块
            await this.updateLastProcessedBlock(network, toBlock, 0);
            return;
        }

        let processedCount = 0;
        let skippedCount = 0;

        for (const log of logs) {
            try {
                const txHash = log.transactionHash;

                // 验证交易安全性（包括重复检查）
                const currentBlock = await provider.getBlockNumber();
                const securityResult = await this.securityManager.validateTransactionSecurity(
                    network, 
                    txHash, 
                    log.blockNumber,
                    currentBlock
                );

                if (!securityResult.isValid) {
                    console.log(`⚠️  跳过交易 ${txHash}: ${securityResult.reason}`);
                    skippedCount++;
                    continue;
                }

                // 解析事件数据并添加到队列
                await this.processHistoricalEvent(log, network);
                processedCount++;

            } catch (error) {
                console.error(`❌ 处理历史事件失败:`, error);
                skippedCount++;
            }
        }

        await this.updateLastProcessedBlock(network, toBlock, logs.length);
        this.logScanResults(network, logs.length, processedCount, skippedCount);
    }

    private async processHistoricalEvent(log: any, network: NetworkName): Promise<void> {
        const tokenAddress = "0x" + log.topics[1].substring(26);
        const sender = "0x" + log.topics[2].substring(26);
        const amount = log.data;
        const txHash = log.transactionHash;

        console.log(`📄 处理遗漏交易: ${txHash}`);
        console.log(`  📍 区块: ${log.blockNumber}`);
        console.log(`  🪙 Token: ${tokenAddress}`);
        console.log(`  👤 Sender: ${sender}`);
        console.log(`  💰 Amount: ${amount}`);

        await this.bridgeQueue.addJob({
            txHash: txHash,
            tokenAddress,
            amount,
            sender,
            network,
            isRescan: true // 标记为重新扫描的交易
        });

        console.log(`✅ 遗漏交易 ${txHash} 已添加到处理队列`);
    }

    private async updateLastProcessedBlock(network: NetworkName, toBlock: number, eventCount: number): Promise<void> {
        try {
            await this.prisma.networkStatus.upsert({
                where: { network },
                update: { lastProcessedBlock: toBlock },
                create: { network, lastProcessedBlock: toBlock },
            });
            console.log(`📝 更新 ${network} 网络最后处理区块为: ${toBlock}`);
        } catch (error) {
            console.error(`❌ 更新最后处理区块失败:`, error);
        }
    }

    private logScanResults(network: NetworkName, totalEvents: number, processed: number, skipped: number): void {
        console.log(`\n📊 ${network} 网络启动扫描完成:`);
        console.log(`  📝 总事件数: ${totalEvents}`);
        console.log(`  ✅ 处理事件数: ${processed}`);
        console.log(`  ⏭️  跳过事件数: ${skipped}`);

        if (processed > 0) {
            console.log(`🎉 成功添加 ${processed} 个遗漏交易到处理队列`);
        }

        if (skipped > 0) {
            console.log(`⚠️  跳过 ${skipped} 个事件（可能已处理或不符合安全要求）`);
        }
    }

    /**
     * 手动扫描指定区块范围的事件（用于调试）
     * @param network 网络名称
     * @param fromBlock 起始区块
     * @param toBlock 结束区块
     * @param addToQueue 是否添加到处理队列
     */
    public async scanBlockRange(
        network: NetworkName,
        fromBlock: number,
        toBlock: number,
        addToQueue: boolean = false
    ): Promise<any[]> {
        console.log(`\n=== 🔍 手动扫描 ${network} 网络 ===`);
        console.log(`扫描范围: ${fromBlock} - ${toBlock}`);
        console.log(`添加到队列: ${addToQueue ? '是' : '否'}`);

        try {
            const provider = this.providerManager.getProvider(network);
            const contract = this.providerManager.getContract(network);

            if (!provider || !contract) {
                throw new Error(`${network} 网络提供者或合约未连接`);
            }

            const bridgeEventSignature = BRIDGE_EVENT_SIGNATURE;

            let logs;
            
            // 对于Bitroot网络，获取所有事件然后手动过滤，避免RPC查询遗漏
            if (network === 'Bitroot') {
                console.log(`🌍 Bitroot网络检测到，使用全事件扫描模式`);
                
                // 获取所有事件
                const allLogs = await provider.getLogs({
                    fromBlock,
                    toBlock,
                });
                
                // 手动过滤出目标合约的Bridge事件
                logs = allLogs.filter(log => 
                    log.address.toLowerCase() === (contract.target as string).toLowerCase() &&
                    log.topics[0] === bridgeEventSignature
                );
                
                console.log(`📝 获取所有事件 ${allLogs.length} 个，过滤出 ${logs.length} 个Bridge事件`);
            } else {
                // 其他网络使用原有的精确查询
                logs = await provider.getLogs({
                    address: contract.target as string,
                    topics: [bridgeEventSignature],
                    fromBlock,
                    toBlock,
                });
                
                console.log(`📝 找到 ${logs.length} 个事件`);
            }

            const events = [];
            for (const log of logs) {
                const eventData = {
                    txHash: log.transactionHash,
                    blockNumber: log.blockNumber,
                    tokenAddress: "0x" + log.topics[1].substring(26),
                    sender: "0x" + log.topics[2].substring(26),
                    amount: log.data,
                    network
                };

                events.push(eventData);
                console.log(`📄 事件: ${eventData.txHash} (区块 ${eventData.blockNumber})`);

                if (addToQueue) {
                    await this.bridgeQueue.addJob({
                        txHash: eventData.txHash,
                        tokenAddress: eventData.tokenAddress,
                        amount: eventData.amount,
                        sender: eventData.sender,
                        network: eventData.network,
                        isRescan: true
                    });
                }
            }

            return events;
        } catch (error) {
            console.error(`❌ 手动扫描失败:`, error);
            throw error;
        }
    }

    /**
     * 获取网络扫描状态
     * @param network 网络名称
     */
    public async getScanStatus(network: NetworkName): Promise<{
        lastProcessedBlock: number;
        latestBlock: number;
        blocksBehind: number;
        needsRescan: boolean;
    } | null> {
        try {
            const provider = this.providerManager.getProvider(network);
            if (!provider) {
                console.error(`❌ ${network} 提供者未连接`);
                return null;
            }

            const [lastProcessed, latestBlock] = await Promise.all([
                this.prisma.networkStatus.findUnique({ where: { network } }),
                provider.getBlockNumber()
            ]);

            const lastProcessedBlock = lastProcessed?.lastProcessedBlock || 0;
            const blocksBehind = latestBlock - lastProcessedBlock;
            const needsRescan = blocksBehind > this.config.config.HEARTBEAT_SYNC_THRESHOLD;

            return {
                lastProcessedBlock,
                latestBlock,
                blocksBehind,
                needsRescan
            };
        } catch (error) {
            console.error(`❌ 获取 ${network} 扫描状态失败:`, error);
            return null;
        }
    }

    /**
     * 测试特定交易是否能被检测到
     * @param network 网络名称
     * @param txHash 交易hash
     * @param blockNumber 区块号
     */
    public async testSpecificTransaction(
        network: NetworkName,
        txHash: string,
        blockNumber: number
    ): Promise<any> {
        console.log(`\n=== 🧪 测试特定交易 ===`);
        console.log(`交易hash: ${txHash}`);
        console.log(`网络: ${network}`);
        console.log(`区块号: ${blockNumber}`);

        try {
            const provider = this.providerManager.getProvider(network);
            const contract = this.providerManager.getContract(network);

            if (!provider || !contract) {
                throw new Error(`${network} 网络提供者或合约未连接`);
            }

            // 1. 获取交易回执
            console.log(`🔍 获取交易回执...`);
            const receipt = await provider.getTransactionReceipt(txHash);
            if (!receipt) {
                console.log(`❌ 未找到交易回执`);
                return null;
            }

            console.log(`✅ 交易回执获取成功:`);
            console.log(`  状态: ${receipt.status === 1 ? '成功' : '失败'}`);
            console.log(`  区块号: ${receipt.blockNumber}`);
            console.log(`  事件数量: ${receipt.logs.length}`);

            // 2. 分析所有事件日志
            const bridgeEventSignature = BRIDGE_EVENT_SIGNATURE;
            console.log(`\n📋 分析事件日志:`);
            console.log(`目标事件签名: ${bridgeEventSignature}`);
            console.log(`目标合约地址: ${contract.target}`);

            const allEvents = [];
            const bridgeEvents = [];

            for (let i = 0; i < receipt.logs.length; i++) {
                const log = receipt.logs[i];
                const eventInfo = {
                    index: i,
                    address: log.address,
                    topics: log.topics,
                    data: log.data,
                    isBridgeEvent: log.address.toLowerCase() === (contract.target as string).toLowerCase() &&
                                   log.topics[0] === bridgeEventSignature
                };

                allEvents.push(eventInfo);

                if (eventInfo.isBridgeEvent) {
                    bridgeEvents.push(eventInfo);
                    console.log(`✅ 找到Bridge事件 #${i}:`);
                    console.log(`  地址: ${log.address}`);
                    console.log(`  Topic0: ${log.topics[0]}`);
                    if (log.topics.length > 1) {
                        console.log(`  Token: 0x${log.topics[1].substring(26)}`);
                    }
                    if (log.topics.length > 2) {
                        console.log(`  Sender: 0x${log.topics[2].substring(26)}`);
                    }
                    console.log(`  Data: ${log.data}`);
                }
            }

            console.log(`\n📊 事件分析结果:`);
            console.log(`  总事件数: ${allEvents.length}`);
            console.log(`  Bridge事件数: ${bridgeEvents.length}`);

            if (bridgeEvents.length === 0) {
                console.log(`\n🔍 详细事件分析:`);
                for (const event of allEvents) {
                    console.log(`事件 #${event.index}:`);
                    console.log(`  地址: ${event.address}`);
                    console.log(`  目标地址: ${contract.target}`);
                    console.log(`  地址匹配: ${event.address.toLowerCase() === (contract.target as string).toLowerCase()}`);
                    console.log(`  Topic0: ${event.topics[0]}`);
                    console.log(`  目标Topic0: ${bridgeEventSignature}`);
                    console.log(`  Topic匹配: ${event.topics[0] === bridgeEventSignature}`);
                    console.log(`---`);
                }
            }

            // 3. 测试使用getLogs查询
            console.log(`\n🔍 测试getLogs查询:`);
            
            // 3.1 Bitroot网络全事件扫描
            if (network === 'Bitroot') {
                console.log(`使用Bitroot网络全事件扫描模式...`);
                const allLogs = await provider.getLogs({
                    fromBlock: blockNumber,
                    toBlock: blockNumber
                });
                
                const filteredLogs = allLogs.filter(log => 
                    log.address.toLowerCase() === (contract.target as string).toLowerCase() &&
                    log.topics[0] === bridgeEventSignature
                );
                
                console.log(`  getLogs所有事件: ${allLogs.length}`);
                console.log(`  过滤后Bridge事件: ${filteredLogs.length}`);
            }

            // 3.2 精确查询
            console.log(`使用精确查询模式...`);
            const preciseLogs = await provider.getLogs({
                address: contract.target as string,
                topics: [bridgeEventSignature],
                fromBlock: blockNumber,
                toBlock: blockNumber
            });
            console.log(`  精确查询Bridge事件: ${preciseLogs.length}`);

            return {
                receipt,
                allEvents,
                bridgeEvents,
                success: bridgeEvents.length > 0
            };

        } catch (error) {
            console.error(`❌ 测试失败:`, error);
            throw error;
        }
    }

    /**
     * 记录扫描操作日志
     */
    public logScanOperation(operation: string, details: any): void {
        console.log(`🔍 扫描操作: ${operation}`);
        console.log(`📋 详情:`, details);
    }
}

export default Scanner; 