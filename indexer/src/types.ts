import { Contract, WebSocketProvider, JsonRpcProvider } from "ethers";

// 网络类型定义
export type NetworkName = "BNB" | "Bitroot";

// 配置接口
export interface Config {
    RESCAN_FROM_BLOCK: Record<NetworkName, number | null>;
    MAX_RESCAN_BLOCKS: number;
    MIN_SYNC_INTERVAL: number;
    MAX_BLOCK_GAP_FOR_SYNC: number;
    HEARTBEAT_SYNC_THRESHOLD: number;
}

// 队列任务数据接口
export interface BridgeJobData {
    txHash: string;
    tokenAddress: string;
    amount: string;
    sender: string;
    network: NetworkName;
    isRescan?: boolean;
}

// 队列任务状态枚举
export enum QueueStatus {
    PENDING = 'PENDING',
    PROCESSING = 'PROCESSING', 
    COMPLETED = 'COMPLETED',
    FAILED = 'FAILED'
}

// 队列统计接口
export interface QueueStats {
    pending: number;
    processing: number;
    completed: number;
    failed: number;
}

// 提供者状态接口
export interface ProvidersState {
    BNB: WebSocketProvider | JsonRpcProvider | null;
    Bitroot: WebSocketProvider | JsonRpcProvider | null;
}

// 合约状态接口
export interface ContractsState {
    BNB: Contract | null;
    Bitroot: Contract | null;
}

// 同步状态接口
export interface SyncState {
    BNB: boolean;
    Bitroot: boolean;
}

// 时间戳状态接口
export interface TimeState {
    BNB: number;
    Bitroot: number;
}

// 监听器状态接口
export interface ListenersState {
    BNB: ((tokenAddress: any, amount: any, sender: any, event: any) => void) | null;
    Bitroot: ((tokenAddress: any, amount: any, sender: any, event: any) => void) | null;
}

// 扫描范围接口
export interface ScanRange {
    scanFromBlock: number;
    scanToBlock: number;
}

// 队列处理结果接口
export interface ProcessResult {
    success: boolean;
    message?: string;
} 