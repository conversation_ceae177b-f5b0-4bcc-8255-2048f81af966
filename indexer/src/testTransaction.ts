import dotenv from "dotenv";
import { NetworkName } from './types';
import { ProviderManager } from './providers';
import { BRIDGE_EVENT_SIGNATURE } from './constants';

dotenv.config();

async function testSpecificTransaction() {
    console.log("🚀 开始测试特定交易...");

    const testTxHash = "0x484e36f49f84c7efaea00ad4594b1049ec05d685db62482956b05cf5d0a27ed9";
    const testNetwork: NetworkName = "Bitroot";

    try {
        // 初始化提供者管理器
        const providerManager = new ProviderManager();
        await providerManager.ensureProviderConnection("Bitroot");
        await providerManager.ensureProviderConnection("BNB");

        console.log(`\n📋 测试交易信息:`);
        console.log(`  Hash: ${testTxHash}`);
        console.log(`  源链: ${testNetwork}`);
        console.log(`  目标链: BNB`);

        // 1. 检查源链（Bitroot）上的交易状态
        console.log(`\n=== 🔍 检查源链 (Bitroot) 交易状态 ===`);
        const bitrootProvider = providerManager.getProvider("Bitroot");
        const bitrootContract = providerManager.getContract("Bitroot");
        
        if (bitrootProvider && bitrootContract) {
            try {
                const receipt = await bitrootProvider.getTransactionReceipt(testTxHash);
                if (receipt) {
                    console.log(`✅ 交易回执获取成功:`);
                    console.log(`  状态: ${receipt.status === 1 ? '成功' : '失败'}`);
                    console.log(`  区块号: ${receipt.blockNumber}`);
                    console.log(`  事件数量: ${receipt.logs.length}`);

                    // 检查Bridge事件
                    const bridgeEventSignature = BRIDGE_EVENT_SIGNATURE;
                    const bridgeEvents = receipt.logs.filter(log => 
                        log.address.toLowerCase() === (bitrootContract.target as string).toLowerCase() &&
                        log.topics[0] === bridgeEventSignature
                    );

                    console.log(`📊 Bridge事件分析:`);
                    console.log(`  总事件数: ${receipt.logs.length}`);
                    console.log(`  Bridge事件数: ${bridgeEvents.length}`);

                    // 详细分析所有事件
                    console.log(`\n📋 详细事件分析:`);
                    receipt.logs.forEach((log, index) => {
                        console.log(`  事件 ${index + 1}:`);
                        console.log(`    地址: ${log.address}`);
                        console.log(`    Topic0: ${log.topics[0]}`);
                        console.log(`    数据: ${log.data}`);
                        console.log(`    是否为目标合约: ${log.address.toLowerCase() === (bitrootContract.target as string).toLowerCase() ? '✅ 是' : '❌ 否'}`);
                        console.log(`    是否为Bridge事件: ${log.topics[0] === bridgeEventSignature ? '✅ 是' : '❌ 否'}`);
                    });

                    if (bridgeEvents.length > 0) {
                        console.log(`✅ 找到Bridge事件，应该触发跨链处理`);
                    } else {
                        console.log(`❌ 未找到Bridge事件`);
                        console.log(`💡 可能原因:`);
                        console.log(`  1. 这不是一个Bridge交易`);
                        console.log(`  2. 事件签名不匹配`);
                        console.log(`  3. 合约地址不正确`);
                    }
                } else {
                    console.log(`❌ 未找到交易回执`);
                }
            } catch (error) {
                console.error(`❌ 获取交易回执失败:`, error.message);
            }
        }

        // 2. 检查目标链（BNB）上的redeem状态
        console.log(`\n=== 🔍 检查目标链 (BNB) redeem状态 ===`);
        const bnbContract = providerManager.getContract("BNB");
        if (bnbContract) {
            try {
                const isProcessed = await bnbContract.isTransactionProcessed(testTxHash);
                console.log(`📊 BNB链上redeem状态: ${isProcessed ? '✅ 已处理' : '⏳ 未处理'}`);
                
                if (isProcessed) {
                    console.log(`🚨 警告：该交易已在BNB链上被redeem处理！`);
                    console.log(`   可能存在重复处理问题`);
                } else {
                    console.log(`✅ 该交易尚未在BNB链上被redeem处理`);
                }
            } catch (error) {
                console.error(`❌ 检查BNB链redeem状态失败:`, error.message);
            }
        }

        // 3. 检查BNB链上是否有相关的redeem交易
        console.log(`\n=== 🔍 检查BNB链上的redeem交易 ===`);
        const bnbProvider = providerManager.getProvider("BNB");
        if (bnbProvider) {
            try {
                // 获取最近的区块来搜索可能的redeem交易
                const latestBlock = await bnbProvider.getBlockNumber();
                console.log(`📊 当前BNB链最新区块: ${latestBlock}`);
                
                // 这里可以添加更详细的搜索逻辑
                console.log(`💡 建议手动检查BNB链上的redeem交易`);
            } catch (error) {
                console.error(`❌ 获取BNB链信息失败:`, error.message);
            }
        }

        // 4. 总结分析
        console.log(`\n=== 📊 重复处理分析总结 ===`);
        console.log(`交易Hash: ${testTxHash}`);
        console.log(`源链: ${testNetwork}`);
        console.log(`目标链: BNB`);
        
        console.log(`\n💡 分析建议:`);
        console.log(`1. 如果BNB链上已处理，检查是否有重复的redeem调用`);
        console.log(`2. 检查索引器日志，确认是否有重复的事件处理`);
        console.log(`3. 检查队列系统，确认是否有重复的任务添加`);
        console.log(`4. 检查数据库中的交易状态记录`);

        // 清理连接
        providerManager.destroyAllProviders();
        
        console.log(`\n✅ 测试完成`);

    } catch (error) {
        console.error("❌ 测试失败:", error);
        process.exit(1);
    }
}

// 运行测试
testSpecificTransaction().catch(error => {
    console.error("启动失败:", error);
    process.exit(1);
});