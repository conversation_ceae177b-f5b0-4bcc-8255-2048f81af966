import { PrismaClient } from "@prisma/client";
import { NetworkName, BridgeJobData, QueueStats, QueueStatus } from './types';
import SecurityManager from './security';
import TransferManager from './transfer';
import { ProviderManager } from './providers';

class DatabaseQueueManager {
    private prisma: PrismaClient;
    private securityManager: SecurityManager;
    private transferManager: TransferManager;
    private providerManager: ProviderManager;
    private isProcessing = false;
    private processingInterval?: NodeJS.Timeout;

    constructor(
        prisma: PrismaClient,
        securityManager: SecurityManager,
        transferManager: TransferManager,
        providerManager: ProviderManager
    ) {
        this.prisma = prisma;
        this.securityManager = securityManager;
        this.transferManager = transferManager;
        this.providerManager = providerManager;
    }

    /**
     * 添加任务到队列
     */
    async addJob(jobData: BridgeJobData): Promise<void> {
        console.log(`📝 添加任务到队列: ${jobData.txHash}`);
        
        try {
            await this.prisma.queueJob.create({
                data: {
                    txHash: jobData.txHash,
                    tokenAddress: jobData.tokenAddress,
                    amount: jobData.amount,
                    sender: jobData.sender,
                    network: jobData.network,
                    isRescan: jobData.isRescan || false,
                    status: QueueStatus.PENDING
                }
            });
            
            console.log(`✅ 任务已添加到队列: ${jobData.txHash}`);
        } catch (error) {
            console.error(`❌ 添加任务到队列失败:`, error);
            throw error;
        }
    }

    /**
     * 启动队列处理器
     */
    async startProcessing(): Promise<void> {
        if (this.isProcessing) {
            console.log("⚠️  队列处理器已在运行");
            return;
        }

        console.log("🔄 启动队列处理器...");
        this.isProcessing = true;

        // 处理启动时遗留的PROCESSING状态的任务
        await this.recoverStuckJobs();

        // 立即处理一次
        await this.processJobs();

        // 设置定期处理
        this.processingInterval = setInterval(async () => {
            if (this.isProcessing) {
                await this.processJobs();
            }
        }, 5000); // 每5秒检查一次

        console.log("✅ 队列处理器已启动");
    }

    /**
     * 停止队列处理器
     */
    async stopProcessing(): Promise<void> {
        console.log("🛑 停止队列处理器...");
        this.isProcessing = false;
        
        if (this.processingInterval) {
            clearInterval(this.processingInterval);
            this.processingInterval = undefined;
        }
        
        console.log("✅ 队列处理器已停止");
    }

    /**
     * 恢复卡住的任务
     */
    private async recoverStuckJobs(): Promise<void> {
        try {
            const stuckJobs = await this.prisma.queueJob.findMany({
                where: {
                    status: QueueStatus.PROCESSING,
                    updatedAt: {
                        lt: new Date(Date.now() - 10 * 60 * 1000) // 10分钟前
                    }
                }
            });

            if (stuckJobs.length > 0) {
                console.log(`🔄 发现 ${stuckJobs.length} 个卡住的任务，重置为PENDING状态`);
                
                await this.prisma.queueJob.updateMany({
                    where: {
                        id: {
                            in: stuckJobs.map(job => job.id)
                        }
                    },
                    data: {
                        status: QueueStatus.PENDING
                    }
                });
            }
        } catch (error) {
            console.error("❌ 恢复卡住任务失败:", error);
        }
    }

    /**
     * 处理队列中的任务
     */
    private async processJobs(): Promise<void> {
        try {
            // 获取待处理的任务
            const pendingJobs = await this.prisma.queueJob.findMany({
                where: {
                    status: QueueStatus.PENDING
                },
                orderBy: {
                    createdAt: 'asc'
                },
                take: 5 // 一次处理5个任务
            });

            if (pendingJobs.length === 0) {
                return;
            }

            console.log(`📋 发现 ${pendingJobs.length} 个待处理任务`);

            // 并行处理任务
            const processPromises = pendingJobs.map(job => this.processJob(job));
            await Promise.allSettled(processPromises);

        } catch (error) {
            console.error("❌ 处理队列任务失败:", error);
        }
    }

    /**
     * 处理单个任务
     */
    private async processJob(job: any): Promise<void> {
        console.log(`🎯 开始处理任务: ${job.txHash}`);

        try {
            // 使用数据库事务来防止竞态条件
            const result = await this.prisma.$transaction(async (tx) => {
                // 检查交易是否已存在并完成
                const existingTransaction = await tx.transactionData.findUnique({
                    where: { txHash: job.txHash }
                });

                if (existingTransaction && existingTransaction.isDone) {
                    console.log(`✅ 交易 ${job.txHash} 已完成，跳过处理`);
                    return { status: 'already_completed' };
                }

                // 检查是否有其他任务正在处理同一个交易
                const processingJob = await tx.queueJob.findFirst({
                    where: {
                        txHash: job.txHash,
                        status: QueueStatus.PROCESSING,
                        id: { not: job.id }
                    }
                });

                if (processingJob) {
                    console.log(`⚠️  交易 ${job.txHash} 正在被其他任务处理，跳过`);
                    return { status: 'being_processed' };
                }

                // 标记为处理中
                await tx.queueJob.update({
                    where: { id: job.id },
                    data: {
                        status: QueueStatus.PROCESSING,
                        attempts: job.attempts + 1
                    }
                });

                // 创建或更新交易记录
                if (!existingTransaction) {
                    await tx.transactionData.create({
                        data: {
                            txHash: job.txHash,
                            tokenAddress: job.tokenAddress,
                            amount: job.amount,
                            sender: job.sender,
                            network: job.network,
                            isDone: false
                        }
                    });
                }

                return { status: 'ready_to_process' };
            });

            if (result.status === 'already_completed') {
                await this.markJobCompleted(job.id);
                return;
            }

            if (result.status === 'being_processed') {
                // 延迟重试
                await this.delay(5000);
                await this.markJobFailed(job.id, 'Transaction being processed by another job', false);
                return;
            }

            // 执行跨链转账
            const targetNetwork = job.network === "BNB" ? "Bitroot" : "BNB";
            console.log(`💸 执行跨链转账: ${job.network} -> ${targetNetwork}`);

            // 执行转账
            await this.transferManager.transferToken(
                job.network === "BNB",
                job.amount,
                job.sender,
                job.txHash
            );

            // 标记交易为完成
            await this.markTransactionComplete(job.txHash);

            // 标记任务为完成
            await this.markJobCompleted(job.id);

            console.log(`✅ 任务处理完成: ${job.txHash}`);

        } catch (error) {
            console.error(`❌ 处理任务失败 ${job.txHash}:`, error);
            await this.markJobFailed(job.id, error.message, job.attempts + 1 >= job.maxAttempts);
        }
    }

    /**
     * 查找已存在的交易
     */
    private async findExistingTransaction(txHash: string) {
        return await this.prisma.transactionData.findUnique({
            where: { txHash }
        });
    }

    /**
     * 标记交易为完成
     */
    private async markTransactionComplete(txHash: string): Promise<void> {
        try {
            await this.prisma.transactionData.update({
                where: { txHash },
                data: { isDone: true }
            });
            console.log(`📝 交易 ${txHash} 已标记为完成`);
        } catch (error) {
            console.error(`❌ 标记交易完成失败:`, error);
        }
    }

    /**
     * 标记任务为完成
     */
    private async markJobCompleted(jobId: number): Promise<void> {
        await this.prisma.queueJob.update({
            where: { id: jobId },
            data: {
                status: QueueStatus.COMPLETED,
                processedAt: new Date()
            }
        });
    }

    /**
     * 标记任务为失败
     */
    private async markJobFailed(jobId: number, error: string, isFinal: boolean): Promise<void> {
        await this.prisma.queueJob.update({
            where: { id: jobId },
            data: {
                status: isFinal ? QueueStatus.FAILED : QueueStatus.PENDING,
                error: error,
                processedAt: isFinal ? new Date() : undefined
            }
        });

        if (isFinal) {
            console.log(`❌ 任务最终失败: ${jobId}, 错误: ${error}`);
        } else {
            console.log(`⚠️  任务失败，将重试: ${jobId}, 错误: ${error}`);
        }
    }

    /**
     * 获取队列统计信息
     */
    async getQueueStats(): Promise<QueueStats> {
        const [pending, processing, completed, failed] = await Promise.all([
            this.prisma.queueJob.count({ where: { status: QueueStatus.PENDING } }),
            this.prisma.queueJob.count({ where: { status: QueueStatus.PROCESSING } }),
            this.prisma.queueJob.count({ where: { status: QueueStatus.COMPLETED } }),
            this.prisma.queueJob.count({ where: { status: QueueStatus.FAILED } })
        ]);

        return { pending, processing, completed, failed };
    }

    /**
     * 清理旧的已完成任务
     */
    async cleanupOldJobs(olderThanDays: number = 7): Promise<void> {
        const cutoffDate = new Date(Date.now() - olderThanDays * 24 * 60 * 60 * 1000);
        
        try {
            const result = await this.prisma.queueJob.deleteMany({
                where: {
                    status: {
                        in: [QueueStatus.COMPLETED, QueueStatus.FAILED]
                    },
                    createdAt: {
                        lt: cutoffDate
                    }
                }
            });

            console.log(`🧹 清理了 ${result.count} 个旧任务`);
        } catch (error) {
            console.error("❌ 清理旧任务失败:", error);
        }
    }

    /**
     * 暂停队列处理
     */
    async pauseQueue(): Promise<void> {
        console.log("⏸️  暂停队列处理");
        this.isProcessing = false;
    }

    /**
     * 恢复队列处理
     */
    async resumeQueue(): Promise<void> {
        console.log("▶️  恢复队列处理");
        this.isProcessing = true;
        await this.processJobs();
    }

    /**
     * 获取处理状态
     */
    isQueueProcessing(): boolean {
        return this.isProcessing;
    }

    /**
     * 兼容旧接口 - 获取队列状态
     */
    async getQueueStatus(): Promise<QueueStats> {
        return this.getQueueStats();
    }

    /**
     * 关闭队列管理器
     */
    async close(): Promise<void> {
        await this.stopProcessing();
    }

    /**
     * 延迟执行
     */
    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

export default DatabaseQueueManager; 