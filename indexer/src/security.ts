import { NetworkName } from './types';
import { PrismaClient } from "@prisma/client";
import ConfigManager from './config';

/**
 * 安全管理器
 * 处理交易验证和防重放攻击
 */
class SecurityManager {
    private prisma: PrismaClient;
    private config = ConfigManager.getInstance();

    constructor(prisma: PrismaClient) {
        this.prisma = prisma;
    }

    /**
     * 验证交易是否已经被处理过，防止重复处理
     * @param network 网络名称
     * @param txHash 交易哈希
     * @param blockNumber 区块号
     * @returns 验证结果
     */
    public async validateTransactionForDuplication(network: NetworkName, txHash: string, blockNumber: number): Promise<boolean> {
        try {
            // 检查交易是否已存在于数据库中
            const existingTx = await this.prisma.transactionData.findUnique({
                where: { txHash }
            });

            if (existingTx) {
                console.log(`🔍 交易 ${txHash} 已存在于数据库中`);
                return false; // 交易已被处理，拒绝重复处理
            }

            console.log(`✅ 交易 ${txHash} 通过重复检查`);
            return true;

        } catch (error) {
            console.error(`❌ 交易重复验证失败:`, error);
            return false;
        }
    }

    /**
     * 验证交易的时间窗口，防止过于陈旧的交易
     * @param blockNumber 区块号
     * @param currentBlock 当前区块号
     * @param maxBlockDiff 最大区块差
     * @returns 验证结果
     */
    public async validateTransactionTiming(
        blockNumber: number, 
        currentBlock: number, 
        maxBlockDiff: number = 10000000000
    ): Promise<boolean> {
        const blockDiff = currentBlock - blockNumber;
        
        if (blockDiff > maxBlockDiff) {
            console.log(`⚠️ 交易区块 ${blockNumber} 距当前区块 ${currentBlock} 差距过大 (${blockDiff} > ${maxBlockDiff})`);
            return false;
        }

        console.log(`✅ 交易时间窗口验证通过 (区块差: ${blockDiff})`);
        return true;
    }

    /**
     * 记录安全事件
     * @param event 事件类型
     * @param details 事件详情
     */
    public logSecurityEvent(event: string, details: any): void {
        const securityLog = {
            timestamp: new Date().toISOString(),
            event,
            details,
            severity: this.getEventSeverity(event)
        };

        console.log(`🔒 安全事件 [${securityLog.severity}]: ${event}`, securityLog);
        
        // TODO: 可以扩展为写入专门的安全日志文件或告警系统
    }

    /**
     * 获取事件严重性级别
     */
    private getEventSeverity(event: string): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
        const highSeverityEvents = ['duplicate_transaction', 'invalid_timing'];
        const mediumSeverityEvents = ['transaction_validation_failed'];
        
        if (highSeverityEvents.some(e => event.includes(e))) return 'HIGH';
        if (mediumSeverityEvents.some(e => event.includes(e))) return 'MEDIUM';
        
        return 'LOW';
    }

    /**
     * 综合验证交易安全性
     */
    public async validateTransactionSecurity(
        network: NetworkName,
        txHash: string,
        blockNumber: number,
        currentBlock?: number
    ): Promise<{ isValid: boolean; reason?: string }> {
        try {
            // 1. 验证是否重复
            const isDuplicateValid = await this.validateTransactionForDuplication(network, txHash, blockNumber);
            if (!isDuplicateValid) {
                this.logSecurityEvent('duplicate_transaction', { network, txHash, blockNumber });
                return { isValid: false, reason: '交易已被处理' };
            }

            // 2. 验证时间窗口（如果提供了当前区块）
            if (currentBlock) {
                const isTimingValid = await this.validateTransactionTiming(blockNumber, currentBlock);
                if (!isTimingValid) {
                    this.logSecurityEvent('invalid_timing', { network, txHash, blockNumber, currentBlock });
                    return { isValid: false, reason: '交易时间窗口无效' };
                }
            }

            return { isValid: true };

        } catch (error) {
            this.logSecurityEvent('transaction_validation_failed', { network, txHash, error: error.message });
            return { isValid: false, reason: '验证过程中发生错误' };
        }
    }

    /**
     * 清理安全日志
     */
    public async cleanupSecurityLogs(): Promise<void> {
        console.log('🧹 清理安全日志...');
        // TODO: 实现安全日志清理逻辑
        console.log('✅ 安全日志清理完成');
    }

    /**
     * 获取安全统计信息
     */
    public async getSecurityStats(): Promise<{
        totalTransactions: number;
        processedTransactions: number;
        pendingTransactions: number;
    }> {
        const [total, processed] = await Promise.all([
            this.prisma.transactionData.count(),
            this.prisma.transactionData.count({ where: { isDone: true } })
        ]);

        return {
            totalTransactions: total,
            processedTransactions: processed,
            pendingTransactions: total - processed
        };
    }
}

export default SecurityManager; 