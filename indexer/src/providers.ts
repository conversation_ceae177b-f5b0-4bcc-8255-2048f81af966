import { Contract, WebSocketProvider, JsonRpcProvider } from "ethers";
import { NetworkName, ProvidersState, ContractsState } from './types';
import { ABI } from "./contract";
import ConfigManager from './config';

export class ProviderManager {
    private providers: ProvidersState = { BNB: null, Bitroot: null };
    private contracts: ContractsState = { BNB: null, Bitroot: null };
    private config = ConfigManager.getInstance();

    public createProvider(networkName: NetworkName): WebSocketProvider | JsonRpcProvider {
        const url = this.config.getNetworkRPC(networkName);
        
        try {
            console.log(`🔗 Creating new connection for ${networkName}...`);

            // 清理现有连接
            if (this.providers[networkName]) {
                try {
                    this.providers[networkName]?.destroy();
                } catch (e) {
                    console.log(`Error destroying existing provider: ${e}`);
                }
            }

            let provider;

            if (url.startsWith('ws')) {
                try {
                    provider = new WebSocketProvider(url);
                    this.setupWebSocketReconnection(provider, url, networkName);
                    console.log(`✅ WebSocket connection for ${networkName} established`);
                } catch (wsError) {
                    console.log(`❌ WebSocket failed for ${networkName}, using HTTP fallback`);
                    const httpUrl = url.replace('wss', 'https').replace('ws', 'http');
                    provider = new JsonRpcProvider(httpUrl);
                }
            } else {
                provider = new JsonRpcProvider(url);
            }

            this.providers[networkName] = provider;
            this.createContract(networkName, provider);

            return provider;

        } catch (error) {
            console.error(`❌ Failed to create provider for ${networkName}:`, error);
            return this.createFallbackProvider(networkName, url);
        }
    }

    private setupWebSocketReconnection(provider: WebSocketProvider, url: string, networkName: NetworkName) {
        // 设置WebSocket重连逻辑（简化版本以避免类型问题）
        try {
            (provider.websocket as any).onclose = () => {
                console.log(`❌ WebSocket connection for ${networkName} closed. Reconnecting in 3 seconds...`);
                
                if (this.providers[networkName] === provider) {
                    this.providers[networkName] = null;
                }

                setTimeout(() => {
                    if (!this.providers[networkName]) {
                        console.log(`🔄 Reconnecting ${networkName} provider...`);
                        this.createProvider(networkName);
                    }
                }, 3000);
            };
        } catch (error) {
            console.log(`⚠️  Could not set up WebSocket reconnection for ${networkName}`);
        }
    }

    private createFallbackProvider(networkName: NetworkName, url: string): JsonRpcProvider {
        const httpUrl = url.replace('wss', 'https').replace('ws', 'http');
        console.log(`🔄 Using fallback HTTP provider for ${networkName}: ${httpUrl}`);
        const provider = new JsonRpcProvider(httpUrl);
        this.providers[networkName] = provider;
        this.createContract(networkName, provider);
        return provider;
    }

    private createContract(networkName: NetworkName, provider: WebSocketProvider | JsonRpcProvider) {
        const contractAddress = this.config.getNetworkBridge(networkName);
        this.contracts[networkName] = new Contract(contractAddress, ABI, provider);
        console.log(`📋 Contract instance created for ${networkName}: ${contractAddress}`);
    }

    public async ensureProviderConnection(networkName: NetworkName): Promise<boolean> {
        const provider = this.providers[networkName];

        if (!provider) {
            console.log(`❌ No provider for ${networkName}, creating new one`);
            this.createProvider(networkName);
            return false;
        }

        try {
            await provider.getBlockNumber();
            return true;
        } catch (error) {
            console.error(`❌ Provider for ${networkName} is not responding. Reconnecting...`);
            this.createProvider(networkName);
            return false;
        }
    }

    public getProvider(network: NetworkName): WebSocketProvider | JsonRpcProvider | null {
        return this.providers[network];
    }

    public getContract(network: NetworkName): Contract | null {
        return this.contracts[network];
    }

    public getAllProviders(): ProvidersState {
        return this.providers;
    }

    public getAllContracts(): ContractsState {
        return this.contracts;
    }

    public destroyAllProviders(): void {
        console.log("🧹 清理所有网络连接...");
        
        for (const network of ["BNB", "Bitroot"] as NetworkName[]) {
            try {
                if (this.providers[network]) {
                    this.providers[network]?.destroy?.();
                    this.providers[network] = null;
                    console.log(`✅ ${network} provider destroyed`);
                }
            } catch (e) {
                console.log(`⚠️  Error closing ${network} provider: ${e.message}`);
            }
        }
        
        console.log("✅ 所有网络连接已清理");
    }

    public async testAllConnections(): Promise<Record<NetworkName, boolean>> {
        const results: Record<NetworkName, boolean> = {} as Record<NetworkName, boolean>;
        
        for (const network of ["BNB", "Bitroot"] as NetworkName[]) {
            try {
                const provider = this.getProvider(network);
                if (provider) {
                    await provider.getBlockNumber();
                    results[network] = true;
                    console.log(`✅ ${network} connection test passed`);
                } else {
                    results[network] = false;
                    console.log(`❌ ${network} provider not found`);
                }
            } catch (error) {
                results[network] = false;
                console.log(`❌ ${network} connection test failed: ${error.message}`);
            }
        }
        
        return results;
    }
} 