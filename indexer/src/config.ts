import { Config, NetworkName } from './types';

const ConfigManager = (() => {
    let instance: ConfigManager | null = null;

    class ConfigManager {
        public config: Config = {
            RESCAN_FROM_BLOCK: {
                BNB: process.env.BNB_RESCAN_FROM_BLOCK ? parseInt(process.env.BNB_RESCAN_FROM_BLOCK) : null,
                Bitroot: process.env.AMOY_RESCAN_FROM_BLOCK ? parseInt(process.env.AMOY_RESCAN_FROM_BLOCK) : null,
            },
            MAX_RESCAN_BLOCKS: parseInt(process.env.MAX_RESCAN_BLOCKS || "1000"),
            MIN_SYNC_INTERVAL: parseInt(process.env.MIN_SYNC_INTERVAL || "5000"),
            MAX_BLOCK_GAP_FOR_SYNC: parseInt(process.env.MAX_BLOCK_GAP_FOR_SYNC || "10"),
            HEARTBEAT_SYNC_THRESHOLD: parseInt(process.env.HEARTBEAT_SYNC_THRESHOLD || "10"),
        };

        // 网络配置
        private networkConfig = {
            BNB: {
                rpc: process.env.BNB_RPC || "",
                bridge: process.env.BNB_BRIDGE || "",
                token: process.env.BNB_TOKEN || "",
            },
            Bitroot: {
                rpc: process.env.AMOY_RPC || "",
                bridge: process.env.AMOY_BRIDGE || "",
                token: process.env.AMOY_TOKEN || "",
            }
        };

        // Rate limit 配置
        private rateLimitConfig = {
            MAX_BLOCKS_PER_QUERY: parseInt(process.env.MAX_BLOCKS_PER_QUERY || "100"),
            BATCH_DELAY_MS: parseInt(process.env.BATCH_DELAY_MS || "100"),
            MAX_RETRIES: parseInt(process.env.MAX_RETRIES || "3"),
            RETRY_DELAY_MS: parseInt(process.env.RETRY_DELAY_MS || "1000"),
        };

        public validateConfig(): void {
            console.log("🔧 验证配置...");
            
            // 验证网络配置
            Object.entries(this.networkConfig).forEach(([network, config]) => {
                if (!config.rpc) console.warn(`⚠️  ${network} RPC 未配置`);
                if (!config.bridge) console.warn(`⚠️  ${network} Bridge 地址未配置`);
                if (!config.token) console.warn(`⚠️  ${network} Token 地址未配置`);
            });

            // 验证私钥
            if (!process.env.PK) {
                console.warn("⚠️  私钥 (PK) 未配置");
            }

            console.log("📋 配置信息:");
            console.log(`  最大重新扫描区块数: ${this.config.MAX_RESCAN_BLOCKS}`);
            console.log(`  最小同步间隔: ${this.config.MIN_SYNC_INTERVAL}ms`);
            console.log(`  最大区块差距: ${this.config.MAX_BLOCK_GAP_FOR_SYNC}`);
            console.log(`  心跳同步阈值: ${this.config.HEARTBEAT_SYNC_THRESHOLD}ms`);
            
            // 显示网络配置状态
            Object.entries(this.networkConfig).forEach(([network, config]) => {
                console.log(`  ${network}: RPC=${!!config.rpc}, Bridge=${!!config.bridge}, Token=${!!config.token}`);
            });
        }

        public getNetworkRPC(network: NetworkName): string {
            return this.networkConfig[network].rpc;
        }

        public getNetworkBridge(network: NetworkName): string {
            return this.networkConfig[network].bridge;
        }

        public getNetworkToken(network: NetworkName): string {
            return this.networkConfig[network].token;
        }

        public getPrivateKey(): string {
            return process.env.PK || "";
        }

        public getRateLimitConfig() {
            return this.rateLimitConfig;
        }
    }

    return {
        getInstance: (): ConfigManager => {
            if (!instance) {
                instance = new ConfigManager();
            }
            return instance;
        }
    };
})();

export default ConfigManager; 