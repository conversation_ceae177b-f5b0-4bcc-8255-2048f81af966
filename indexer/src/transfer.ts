import { Contract, WebSocketProvider, Wallet, JsonRpcProvider } from "ethers";
import { NetworkName } from './types';
import { ABI } from "./contract";
import ConfigManager from './config';

class TransferManager {
    private config = ConfigManager.getInstance();

    /**
     * 执行跨链转账
     * @param isBNB 是否在BNB网络上执行转账
     * @param amount 转账金额
     * @param sender 发送者地址
     * @param txHash 源链交易哈希
     * @returns 交易回执
     */
    public async transferToken(
        isBNB: boolean,
        amount: string,
        sender: string,
        txHash: string
    ): Promise<any> {
        console.log(`\n=== 🌉 开始执行跨链转账 ===`);
        console.log(`源网络: ${isBNB ? 'BNB' : 'Bitroot'}`);
        console.log(`目标网络: ${!isBNB ? 'BNB' : 'Bitroot'}`);
        console.log(`发送者: ${sender}`);
        console.log(`金额: ${amount}`);
        console.log(`源链交易哈希: ${txHash}`);

        let provider = null;

        try {
            // 获取目标网络配置
            const targetNetwork: NetworkName = !isBNB ? 'BNB' : 'Bitroot';
            const RPC = this.config.getNetworkRPC(targetNetwork);
            const pk = this.config.getPrivateKey();
            
            this.validateTransferParams(RPC, pk, targetNetwork);

            // 创建提供者和钱包
            provider = this.createTransferProvider(RPC);
            const wallet = new Wallet(pk, provider);
            console.log(`🔑 钱包地址: ${wallet.address}`);
            
            // 创建合约实例
            const contractInstance = this.createContractInstance(targetNetwork, wallet);
            const testToken = this.config.getNetworkToken(targetNetwork);

            // 解析金额
            const parsedAmount = this.parseAmount(amount);

            // 1. 检查合约余额是否足够
            console.log(`💰 检查合约余额是否足够...`);
            const hasEnoughBalance = await this.checkContractBalance(contractInstance, testToken, parsedAmount, targetNetwork);
            if (!hasEnoughBalance) {
                const errorMsg = `合约余额不足，无法执行转账`;
                console.error(`❌ ${errorMsg}`);
                throw new Error(`INSUFFICIENT_CONTRACT_BALANCE: ${errorMsg}`);
            }

            // 2. 检查代币是否在白名单中
            console.log(`📋 检查代币白名单状态...`);
            const isWhitelisted = await this.checkTokenWhitelist(contractInstance, testToken);
            if (!isWhitelisted) {
                const errorMsg = `代币 ${testToken} 未在合约白名单中`;
                console.error(`❌ ${errorMsg}`);
                throw new Error(`TOKEN_NOT_WHITELISTED: ${errorMsg}`);
            }

            // 3. 检查交易是否已被处理
            console.log(`🔍 检查交易是否已被处理...`);
            const isProcessed = await this.checkTransactionProcessed(contractInstance, txHash);
            if (isProcessed) {
                const errorMsg = `交易 ${txHash} 已被处理过`;
                console.error(`❌ ${errorMsg}`);
                throw new Error(`TRANSACTION_ALREADY_PROCESSED: ${errorMsg}`);
            }

            // 执行转账
            console.log(`📝 正在执行 redeem 交易...`);
            console.log(`参数: Token=${testToken}, Sender=${sender}, Amount=${parsedAmount}, TxHash=${txHash}`);

            // 获取当前 nonce
            const nonce = await wallet.getNonce();
            console.log(`🔢 当前 nonce: ${nonce}`);

            // 获取当前 gas 价格并提高 20%
            const gasPrice = await provider.getFeeData();
            const adjustedGasPrice = gasPrice.gasPrice ? gasPrice.gasPrice * BigInt(120) / BigInt(100) : undefined;
            console.log(`⛽ 调整后的 gas 价格: ${adjustedGasPrice?.toString()}`);

            const tx = await contractInstance.redeem(testToken, sender, parsedAmount, txHash, {
                nonce: nonce,
                gasPrice: adjustedGasPrice
            });
            console.log(`✅ 交易已发送: ${tx.hash}`);

            console.log(`⏳ 等待交易确认...`);
            const receipt = await tx.wait();
            console.log(`✅ 交易已确认，区块号: ${receipt.blockNumber}`);
            console.log(`⛽ Gas 使用量: ${receipt.gasUsed?.toString()}`);
            
            return receipt;
        } catch (error) {
            // 详细的错误分类和记录
            const errorInfo = this.categorizeError(error, {
                targetNetwork: !isBNB ? 'BNB' : 'Bitroot',
                sender,
                amount,
                txHash
            });
            
            console.error("❌ transferToken 执行失败:", error);
            console.error("🔍 错误分类:", errorInfo.category);
            console.error("📋 错误详情:", errorInfo.details);
            console.error("🚨 是否可重试:", errorInfo.isRetryable ? '是' : '否');
            
            // 抛出包含分类信息的错误
            const enhancedError = new Error(error.message);
            enhancedError.name = errorInfo.category;
            (enhancedError as any).errorInfo = errorInfo;
            throw enhancedError;
        } finally {
            if (provider) {
                try {
                    provider.destroy();
                    console.log(`🧹 提供者已清理`);
                } catch (e) {
                    // Ignore cleanup errors
                }
            }
        }
    }

    private validateTransferParams(RPC: string, pk: string, networkName: NetworkName): void {
        console.log(`🔍 验证转账参数...`);
        console.log(`RPC URL: ${RPC}`);
        console.log(`私钥存在: ${pk ? '是' : '否'}`);
        
        if (!pk) {
            console.error("❌ 私钥未配置！请检查环境变量 PK");
            throw new Error("私钥未配置");
        }

        if (!RPC) {
            console.error(`❌ ${networkName} RPC未配置！`);
            throw new Error("RPC未配置");
        }

        const contractAddress = this.config.getNetworkBridge(networkName);
        console.log(`合约地址: ${contractAddress}`);
            
        if (!contractAddress) {
            console.error(`❌ ${networkName} 合约地址未配置！`);
            throw new Error("合约地址未配置");
        }

        const tokenAddress = this.config.getNetworkToken(networkName);
        console.log(`代币地址: ${tokenAddress}`);
            
        if (!tokenAddress) {
            console.error(`❌ ${networkName} 代币地址未配置！`);
            throw new Error("代币地址未配置");
        }
    }

    private createTransferProvider(RPC: string): WebSocketProvider | JsonRpcProvider {
        console.log(`🔗 正在创建转账提供者...`);

        if (RPC.startsWith('ws')) {
            try {
                console.log(`使用 WebSocket 连接`);
                return new WebSocketProvider(RPC);
            } catch (error) {
                console.log(`WebSocket 连接失败，使用 HTTP 连接`);
                const httpUrl = RPC.replace('wss', 'https').replace('ws', 'http');
                return new JsonRpcProvider(httpUrl);
            }
        } else {
            console.log(`使用 HTTP 连接`);
            return new JsonRpcProvider(RPC);
        }
    }

    private createContractInstance(networkName: NetworkName, wallet: Wallet): Contract {
        const contractAddress = this.config.getNetworkBridge(networkName);
        console.log(`📋 创建合约实例: ${contractAddress}`);
        return new Contract(contractAddress, ABI, wallet);
    }

    private parseAmount(amount: string): string {
        let parsedAmount = amount;
        if (amount.startsWith('0x')) {
            parsedAmount = BigInt(amount).toString();
            console.log(`🔢 转换后的金额: ${parsedAmount}`);
        } else {
            console.log(`🔢 原始金额: ${parsedAmount}`);
        }
        return parsedAmount;
    }

    /**
     * 验证转账参数的完整性
     * @param isBNB 是否在BNB网络上执行
     * @param amount 金额
     * @param sender 发送者
     * @param txHash 交易哈希
     * @returns 验证结果
     */
    public validateTransferRequest(
        isBNB: boolean,
        amount: string,
        sender: string,
        txHash: string
    ): { isValid: boolean; errors: string[] } {
        const errors: string[] = [];

        // 验证金额
        if (!amount || amount === '0') {
            errors.push('金额不能为空或零');
        }

        try {
            if (amount.startsWith('0x')) {
                BigInt(amount);
            } else {
                const num = parseFloat(amount);
                if (isNaN(num) || num <= 0) {
                    errors.push('金额格式无效');
                }
            }
        } catch {
            errors.push('金额格式无效');
        }

        // 验证发送者地址
        if (!sender || !sender.startsWith('0x') || sender.length !== 42) {
            errors.push('发送者地址格式无效');
        }

        // 验证交易哈希
        if (!txHash || !txHash.startsWith('0x') || txHash.length !== 66) {
            errors.push('交易哈希格式无效');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 估算转账Gas费用
     * @param isBNB 是否在BNB网络上执行
     * @param amount 金额
     * @param sender 发送者
     * @param txHash 交易哈希
     * @returns Gas估算结果
     */
    public async estimateTransferGas(
        isBNB: boolean,
        amount: string,
        sender: string,
        txHash: string
    ): Promise<{ gasLimit: string; gasPrice: string; estimatedCost: string } | null> {
        try {
            const targetNetwork: NetworkName = !isBNB ? 'BNB' : 'Bitroot';
            const RPC = this.config.getNetworkRPC(targetNetwork);
            const pk = this.config.getPrivateKey();
            
            const provider = this.createTransferProvider(RPC);
            const wallet = new Wallet(pk, provider);
            const contractInstance = this.createContractInstance(targetNetwork, wallet);
            const testToken = this.config.getNetworkToken(targetNetwork);
            const parsedAmount = this.parseAmount(amount);

            const gasLimit = await contractInstance.redeem.estimateGas(
                testToken,
                sender,
                parsedAmount,
                txHash
            );

            const gasPrice = await provider.getFeeData();
            const estimatedCost = gasLimit * (gasPrice.gasPrice || BigInt(0));

            return {
                gasLimit: gasLimit.toString(),
                gasPrice: gasPrice.gasPrice?.toString() || '0',
                estimatedCost: estimatedCost.toString()
            };
        } catch (error) {
            console.error('❌ Gas估算失败:', error);
            return null;
        }
    }

    public logTransferOperation(operation: string, details: any): void {
        console.log(`💸 转账操作: ${operation}`);
        console.log(`📋 详情:`, details);
    }

    private async checkContractBalance(
        contractInstance: Contract, 
        tokenAddress: string, 
        amount: string, 
        network: NetworkName
    ): Promise<boolean> {
        try {
            // 创建ERC20合约实例来检查余额
            const erc20ABI = [
                "function balanceOf(address owner) view returns (uint256)"
            ];
            
            const tokenContract = new Contract(
                tokenAddress, 
                erc20ABI, 
                contractInstance.runner
            );
            
            const balance = await tokenContract.balanceOf(contractInstance.target);
            const requiredAmount = BigInt(amount);
            
            console.log(`💰 ${network} 合约余额检查:`);
            console.log(`  代币地址: ${tokenAddress}`);
            console.log(`  合约余额: ${balance.toString()}`);
            console.log(`  需要金额: ${requiredAmount.toString()}`);
            console.log(`  余额充足: ${balance >= requiredAmount ? '✅ 是' : '❌ 否'}`);
            
            if (balance < requiredAmount) {
                const shortage = requiredAmount - balance;
                console.error(`❌ 余额不足，缺少: ${shortage.toString()}`);
                return false;
            }
            
            return true;
        } catch (error) {
            console.error(`❌ 检查合约余额失败:`, error.message);
            // 如果无法检查余额，为安全起见返回false
            return false;
        }
    }

    private async checkTokenWhitelist(contractInstance: Contract, tokenAddress: string): Promise<boolean> {
        try {
            const isWhitelisted = await contractInstance.whitelistedTokens(tokenAddress);
            console.log(`📋 代币白名单检查: ${tokenAddress} -> ${isWhitelisted ? '✅ 已列入' : '❌ 未列入'}`);
            return isWhitelisted;
        } catch (error) {
            console.error(`❌ 检查代币白名单失败:`, error.message);
            return false;
        }
    }

    /**
     * 检查交易是否已被处理
     */
    private async checkTransactionProcessed(contractInstance: Contract, txHash: string): Promise<boolean> {
        try {
            const isProcessed = await contractInstance.isTransactionProcessed(txHash);
            console.log(`🔍 交易处理状态检查: ${txHash} -> ${isProcessed ? '✅ 已处理' : '⏳ 未处理'}`);
            return isProcessed;
        } catch (error) {
            console.error(`❌ 检查交易处理状态失败:`, error.message);
            // 如果无法检查，为安全起见假设未处理
            return false;
        }
    }

    private categorizeError(error: any, context: any): {
        category: string;
        details: string;
        isRetryable: boolean;
        context: any;
    } {
        const errorMessage = error.message?.toLowerCase() || '';
        const errorData = error.data || '';

        // 合约特定错误
        if (errorMessage.includes('BridgeContract__Transaction_Already_Processed') || 
            errorData.includes('0x')) {
            return {
                category: 'TRANSACTION_ALREADY_PROCESSED',
                details: '交易已被处理过，无法重复执行',
                isRetryable: false,
                context
            };
        }

        if (errorMessage.includes('BridgeContract__Token_Not_Whitelisted')) {
            return {
                category: 'TOKEN_NOT_WHITELISTED',
                details: '代币未在白名单中，无法执行转账',
                isRetryable: false,
                context
            };
        }

        if (errorMessage.includes('BridgeContract__Transaction_Failed')) {
            return {
                category: 'TRANSFER_FAILED',
                details: '代币转账失败，可能是余额不足或权限问题',
                isRetryable: true,
                context
            };
        }

        // 网络相关错误
        if (errorMessage.includes('network') || errorMessage.includes('connection')) {
            return {
                category: 'NETWORK_ERROR',
                details: '网络连接问题，可以重试',
                isRetryable: true,
                context
            };
        }

        // Gas相关错误
        if (errorMessage.includes('gas') || errorMessage.includes('out of gas')) {
            return {
                category: 'GAS_ERROR',
                details: 'Gas费用不足或Gas限制问题',
                isRetryable: true,
                context
            };
        }

        // 余额不足错误
        if (errorMessage.includes('insufficient') || errorMessage.includes('balance')) {
            return {
                category: 'INSUFFICIENT_BALANCE',
                details: '余额不足，无法执行转账',
                isRetryable: false,
                context
            };
        }

        // Nonce 相关错误
        if (errorMessage.includes('nonce') || errorMessage.includes('sequence') || error.code === 'NONCE_EXPIRED') {
            return {
                category: 'NONCE_ERROR',
                details: 'Nonce 错误，需要重新获取 nonce',
                isRetryable: true,
                context
            };
        }

        // Gas 费用相关错误
        if (errorMessage.includes('replacement') || errorMessage.includes('underpriced') || error.code === 'REPLACEMENT_UNDERPRICED') {
            return {
                category: 'GAS_ERROR',
                details: 'Gas 费用不足，需要提高 gas 费用',
                isRetryable: true,
                context
            };
        }

        // 默认分类
        return {
            category: 'UNKNOWN_ERROR',
            details: `未知错误: ${error.message}`,
            isRetryable: true,
            context
        };
    }
}

export default TransferManager; 