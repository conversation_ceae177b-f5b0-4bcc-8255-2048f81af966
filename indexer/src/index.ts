import dotenv from "dotenv";
import { NetworkName } from './types';
import DatabaseManager from './database';
import { ProviderManager } from './providers';
import SecurityManager from './security';
import Scanner from './scanner';
import TransferManager from './transfer';
import DatabaseQueueManager from './databaseQueue';
import ConfigManager from './config';
import ApiServer from './apiServer';

dotenv.config();

/**
 * 🌉 跨链桥索引器主类
 * 
 * 负责协调各个模块，管理整个跨链桥监听和处理流程
 */
class BridgeIndexer {
    private databaseManager: DatabaseManager;
    private providerManager: ProviderManager;
    private securityManager: SecurityManager;
    private scanner: Scanner;
    private transferManager: TransferManager;
    private queueManager: DatabaseQueueManager;
    private apiServer: ApiServer;
    private config = ConfigManager.getInstance();
    private isShuttingDown = false;
    private healthCheckInterval?: NodeJS.Timeout;

    constructor() {
        console.log("🚀 初始化跨链桥索引器...");

        // 初始化基础模块
        this.databaseManager = new DatabaseManager();
        this.providerManager = new ProviderManager();
        this.transferManager = new TransferManager();

        // 获取Prisma客户端并初始化依赖模块
        const prisma = this.databaseManager.getPrismaClient();
        this.securityManager = new SecurityManager(prisma);
        this.queueManager = new DatabaseQueueManager(prisma, this.securityManager, this.transferManager, this.providerManager);

        // 初始化扫描器
        this.scanner = new Scanner(prisma, this.providerManager, this.securityManager, this.queueManager);

        // 初始化API服务器
        this.apiServer = new ApiServer(prisma, this.providerManager, this.securityManager, this.queueManager);

        console.log("✅ 所有模块初始化完成");
    }

    /**
     * 启动系统的主要入口点
     */
    public async start(): Promise<void> {
        try {
            console.log("\n=== 🌉 跨链桥索引器启动序列 ===");

            // 记录配置信息
            this.config.validateConfig();

            // 1. 初始化数据库
            await this.initializeDatabase();

            // 2. 验证网络配置
            await this.validateNetworkConfigurations();

            // 3. 初始化网络提供者
            await this.initializeProviders();

            // 3.5. 确保队列处于工作状态
            await this.ensureQueueReady();

            // 4. 执行启动扫描
            await this.performStartupScan();

            // 5. 设置健康检查
            this.setupHealthChecks();

            // 6. 启动API服务器
            this.startApiServer();

            // 7. 设置进程信号处理
            this.setupProcessHandlers();

            console.log("\n🎉 跨链桥索引器启动完成！");
            console.log("📊 系统状态: 运行中");
            console.log("🔄 正在监听跨链交易...");

        } catch (error) {
            console.error("❌ 跨链桥索引器启动失败:", error);
            await this.shutdown();
            process.exit(1);
        }
    }

    private async initializeDatabase(): Promise<void> {
        console.log("\n1️⃣ 初始化数据库...");
        await this.databaseManager.initializeDatabase();

        // 显示数据库统计信息
        const stats = await this.databaseManager.getNetworkStats();
        if (stats) {
            console.log("📊 数据库统计:");
            console.log(`  网络状态记录: ${stats.networkStatus.length}`);
            stats.transactionsByNetwork.forEach(stat => {
                console.log(`  ${stat.network} 网络交易数: ${stat._count._all}`);
            });
        }
    }

    private async validateNetworkConfigurations(): Promise<void> {
        console.log("\n2️⃣ 验证网络配置...");

        for (const network of ["BNB", "Bitroot"] as NetworkName[]) {
            try {
                const rpc = this.config.getNetworkRPC(network);
                const bridge = this.config.getNetworkBridge(network);
                const token = this.config.getNetworkToken(network);
                const pk = this.config.getPrivateKey();

                if (!rpc || !bridge || !token || !pk) {
                    throw new Error(`${network} 网络配置不完整`);
                }

                console.log(`✅ ${network} 网络配置验证通过`);
            } catch (error) {
                console.error(`❌ ${network} 网络配置验证失败:`, error.message);
                throw error;
            }
        }
    }

    private async initializeProviders(): Promise<void> {
        console.log("\n3️⃣ 初始化网络提供者...");

        // 创建提供者
        this.providerManager.createProvider("BNB");
        this.providerManager.createProvider("Bitroot");

        // 测试连接
        const connectionResults = await this.providerManager.testAllConnections();

        for (const [network, isConnected] of Object.entries(connectionResults)) {
            if (isConnected) {
                console.log(`✅ ${network} 网络连接成功`);
            } else {
                console.error(`❌ ${network} 网络连接失败`);
                throw new Error(`${network} 网络连接失败`);
            }
        }
    }

    private async ensureQueueReady(): Promise<void> {
        console.log("\n3️⃣.5️⃣ 启动队列处理器...");
        
        try {
            // 启动队列处理器
            await this.queueManager.startProcessing();
            console.log("✅ 队列处理器已启动");
            
            // 检查队列状态
            const queueStatus = await this.queueManager.getQueueStatus();
            console.log(`📋 队列当前状态: 等待${queueStatus.pending} 处理中${queueStatus.processing} 完成${queueStatus.completed} 失败${queueStatus.failed}`);
            
            // 如果有等待中的任务，记录一下
            if (queueStatus.pending > 0) {
                console.log(`🔔 发现 ${queueStatus.pending} 个等待处理的任务，队列将自动开始处理`);
            }
            
            // 如果有失败的任务，给出提示
            if (queueStatus.failed > 0) {
                console.log(`⚠️  发现 ${queueStatus.failed} 个失败的任务，可以通过重试机制处理`);
            }
            
        } catch (error) {
            console.error("❌ 队列状态检查失败:", error);
            throw error;
        }
    }

    private async performStartupScan(): Promise<void> {
        console.log("\n4️⃣ 执行启动时扫描...");

        // 并行执行两个网络的扫描
        const networks: NetworkName[] = ["BNB", "Bitroot"];
        const scanPromises = networks.map(async (network: NetworkName) => {
            try {
                await this.scanner.performStartupRescan(network, true);
            } catch (error) {
                console.error(`❌ ${network} 网络启动扫描失败:`, error.message);
                // 不阻断启动流程，记录错误并继续
            }
        });

        await Promise.all(scanPromises);
        console.log("✅ 启动扫描完成");

        // 显示队列状态
        const queueStatus = await this.queueManager.getQueueStatus();
        console.log("📋 队列状态:");
        console.log(`  等待处理: ${queueStatus.pending}`);
        console.log(`  正在处理: ${queueStatus.processing}`);
        console.log(`  已完成: ${queueStatus.completed}`);
        console.log(`  失败: ${queueStatus.failed}`);
    }

    private setupHealthChecks(): void {
        console.log("\n5️⃣ 设置健康检查...");

        // 每60秒执行一次健康检查
        this.healthCheckInterval = setInterval(async () => {
            if (this.isShuttingDown) return;

            try {
                await this.performHealthCheck();
            } catch (error) {
                console.error("❌ 健康检查失败:", error.message);
            }
        }, 60000);

        console.log("✅ 健康检查已设置");
    }

    private startApiServer(): void {
        console.log("\n6️⃣ 启动API服务器...");
        const port = parseInt(process.env.API_PORT || "3001", 10);
        this.apiServer.start(port);
        console.log("✅ API服务器启动完成");
    }

    private async performHealthCheck(): Promise<void> {
        const timestamp = new Date().toISOString();
        console.log(`\n🔍 [${timestamp}] 执行健康检查...`);

        // 1. 检查数据库健康状态
        const dbHealth = await this.databaseManager.getHealthStatus();
        console.log(`💾 数据库: ${dbHealth.isHealthy ? '✅ 健康' : '❌ 异常'} (延迟: ${dbHealth.latency}ms)`);

        // 2. 检查网络连接
        const connections = await this.providerManager.testAllConnections();
        for (const [network, isConnected] of Object.entries(connections)) {
            console.log(`🌐 ${network}: ${isConnected ? '✅ 连接正常' : '❌ 连接异常'}`);
        }

        // 3. 检查队列状态并自动恢复
        await this.checkAndRecoverQueue();

        // 4. 检查扫描状态
        for (const network of ["BNB", "Bitroot"] as NetworkName[]) {
            const scanStatus = await this.scanner.getScanStatus(network);
            if (scanStatus) {
                console.log(`🔍 ${network}: 最新区块 ${scanStatus.latestBlock}, 落后 ${scanStatus.blocksBehind} 个区块`);

                // 如果落后太多，触发扫描
                if (scanStatus.needsRescan) {
                    console.log(`⚠️  ${network} 落后较多，触发重新扫描...`);
                    this.scanner.performStartupRescan(network).catch(error => {
                        console.error(`❌ ${network} 重新扫描失败:`, error.message);
                    });
                }
            }
        }
    }

    /**
     * 检查队列状态并在必要时自动恢复
     */
    private async checkAndRecoverQueue(): Promise<void> {
        try {
            const queueStatus = await this.queueManager.getQueueStatus();
            console.log(`📋 队列: ⏳${queueStatus.pending} 🔄${queueStatus.processing} ✅${queueStatus.completed} ❌${queueStatus.failed}`);

            // 检查队列是否有待处理任务但没有处理中任务
            if (queueStatus.pending > 0 && queueStatus.processing === 0) {
                console.log(`🔧 检测到队列可能停滞（${queueStatus.pending} 个等待任务，但无处理中任务），尝试恢复...`);
                
                try {
                    await this.queueManager.resumeQueue();
                    console.log(`✅ 队列恢复命令已执行`);
                    
                    // 再次检查状态
                    const newStatus = await this.queueManager.getQueueStatus();
                    if (newStatus.processing > 0 || newStatus.pending < queueStatus.pending) {
                        console.log(`🎉 队列恢复成功，当前处理中任务: ${newStatus.processing}`);
                    } else {
                        console.log(`⚠️  队列恢复后状态未改变，可能需要手动干预`);
                    }
                } catch (error) {
                    console.error(`❌ 队列恢复失败:`, error.message);
                }
            }

            // 监控失败任务过多的情况
            if (queueStatus.failed > 10) {
                console.log(`⚠️  检测到大量失败任务(${queueStatus.failed})，建议检查系统状态`);
            }

            // 监控队列长期无活动的情况
            if (queueStatus.pending === 0 && queueStatus.processing === 0 && queueStatus.completed === 0) {
                console.log(`ℹ️  队列当前无任务，系统空闲中`);
            }

        } catch (error) {
            console.error(`❌ 队列状态检查失败:`, error.message);
        }
    }

    private setupProcessHandlers(): void {
        console.log("\n6️⃣ 设置进程信号处理...");

        // 优雅关闭处理
        process.on('SIGINT', () => {
            console.log('\n⚠️  收到 SIGINT 信号，开始优雅关闭...');
            this.shutdown();
        });

        process.on('SIGTERM', () => {
            console.log('\n⚠️  收到 SIGTERM 信号，开始优雅关闭...');
            this.shutdown();
        });

        // 错误处理
        process.on('uncaughtException', (error) => {
            console.error('❌ 未捕获的异常:', error);
            this.securityManager.logSecurityEvent('UNCAUGHT_EXCEPTION', { error: error.message, stack: error.stack });
            // 不立即退出，记录错误并继续运行
        });

        process.on('unhandledRejection', (reason, promise) => {
            console.error('❌ 未处理的 Promise 拒绝:', reason);
            this.securityManager.logSecurityEvent('UNHANDLED_REJECTION', { reason, promise });
            // 不立即退出，记录错误并继续运行
        });

        console.log("✅ 进程信号处理已设置");
    }

    /**
     * 优雅关闭系统
     */
    private async shutdown(): Promise<void> {
        if (this.isShuttingDown) return;

        this.isShuttingDown = true;
        console.log('\n🔄 正在关闭跨链桥索引器...');

        try {
            // 1. 停止健康检查
            if (this.healthCheckInterval) {
                clearInterval(this.healthCheckInterval);
                console.log('✅ 健康检查已停止');
            }

            // 2. 停止队列处理
            await this.queueManager.stopProcessing();
            console.log('✅ 队列处理已停止');

            // 3. 清理提供者连接
            this.providerManager.destroyAllProviders();
            console.log('✅ 网络提供者已清理');

            // 4. 关闭队列
            await this.queueManager.close();
            console.log('✅ 队列已关闭');

            // 5. 断开数据库连接
            await this.databaseManager.disconnect();
            console.log('✅ 数据库连接已断开');

            console.log('🎉 跨链桥索引器已优雅关闭');
            process.exit(0);

        } catch (error) {
            console.error('❌ 关闭过程中发生错误:', error);
            process.exit(1);
        }
    }


    /**
     * 获取系统状态
     */
    public async getSystemStatus(): Promise<any> {
        const [dbHealth, connections, queueStatus] = await Promise.all([
            this.databaseManager.getHealthStatus(),
            this.providerManager.testAllConnections(),
            this.queueManager.getQueueStatus()
        ]);

        const networkStatus = {};
        for (const network of ["BNB", "Bitroot"] as NetworkName[]) {
            networkStatus[network] = await this.scanner.getScanStatus(network);
        }

        return {
            timestamp: new Date().toISOString(),
            database: dbHealth,
            networks: connections,
            queue: queueStatus,
            scanning: networkStatus,
            isShuttingDown: this.isShuttingDown
        };
    }
}

// 启动应用
const bridgeIndexer = new BridgeIndexer();
bridgeIndexer.start().catch(error => {
    console.error("启动失败:", error);
    process.exit(1);
});

// 导出实例供其他模块使用 (如果需要)
export default bridgeIndexer;
