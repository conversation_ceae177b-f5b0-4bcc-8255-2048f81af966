# 使用官方Node.js 20镜像
FROM node:20-alpine

# 设置工作目录
WORKDIR /app

# 创建非root用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S indexer -u 1001

# 安装必要的系统依赖
RUN apk add --no-cache openssl git

# 复制package.json和yarn.lock
COPY package.json yarn.lock ./

# 使用npm安装依赖
RUN npm install --omit=dev

# 复制prisma配置
COPY prisma/ ./prisma/

# 生成Prisma客户端
RUN npx prisma generate

# 复制编译后的代码
COPY dist/ ./dist/

# 更改文件所有者
RUN chown -R indexer:nodejs /app
USER indexer

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "console.log('Health check')" || exit 1

# 启动应用
CMD ["node", "dist/index.js"] 