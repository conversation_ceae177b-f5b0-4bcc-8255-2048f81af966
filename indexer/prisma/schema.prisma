// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema
// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum NETWORK {
    BNB
    Bitroot
}

model TransactionData {
  id           Int      @id @default(autoincrement())
  txHash       String   @unique
  isDone       Boolean  @default(false)
  tokenAddress String
  amount       String
  sender       String
  network      String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
}

model NetworkStatus {
  id                 Int      @id @default(autoincrement())
  network            String   @unique
  lastProcessedBlock Int
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt
}

enum QUEUE_STATUS {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
}

model Queue<PERSON>ob {
  id           Int          @id @default(autoincrement())
  txHash       String
  tokenAddress String
  amount       String
  sender       String
  network      String
  isRescan     Boolean      @default(false)
  status       QUEUE_STATUS @default(PENDING)
  attempts     Int          @default(0)
  maxAttempts  Int          @default(3)
  error        String?
  processedAt  DateTime?
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt

  @@index([status, createdAt])
}
