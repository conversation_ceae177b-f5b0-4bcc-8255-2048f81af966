/*
  Warnings:

  - You are about to drop the column `nonce` on the `TransactionData` table. All the data in the column will be lost.
  - You are about to drop the `Nonce` table. If the table is not empty, all the data it contains will be lost.
  - Changed the type of `network` on the `NetworkStatus` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- CreateEnum
CREATE TYPE "NETWORK" AS ENUM ('BNB', 'Bitroot');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "QUEUE_STATUS" AS ENUM ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED');

-- AlterTable
ALTER TABLE "NetworkStatus" DROP COLUMN "network",
ADD COLUMN     "network" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "TransactionData" DROP COLUMN "nonce";

-- DropTable
DROP TABLE "Nonce";

-- DropEnum
DROP TYPE "NETWOEK";

-- CreateTable
CREATE TABLE "QueueJob" (
    "id" SERIAL NOT NULL,
    "txHash" TEXT NOT NULL,
    "tokenAddress" TEXT NOT NULL,
    "amount" TEXT NOT NULL,
    "sender" TEXT NOT NULL,
    "network" TEXT NOT NULL,
    "isRescan" BOOLEAN NOT NULL DEFAULT false,
    "status" "QUEUE_STATUS" NOT NULL DEFAULT 'PENDING',
    "attempts" INTEGER NOT NULL DEFAULT 0,
    "maxAttempts" INTEGER NOT NULL DEFAULT 3,
    "error" TEXT,
    "processedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "QueueJob_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "QueueJob_status_createdAt_idx" ON "QueueJob"("status", "createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "NetworkStatus_network_key" ON "NetworkStatus"("network");
