# 数据库配置
# PostgreSQL 数据库连接字符串
DATABASE_URL="postgresql://username:password@localhost:5432/bridge_db"

# 区块链网络 RPC 配置
# BNB Smart Chain 测试网 RPC URL (支持 WebSocket 或 HTTP)
BNB_RPC="wss://bsc-testnet.bnbchain.org/ws"
# 或者使用 HTTP: BNB_RPC="https://bsc-testnet.bnbchain.org"

# Polygon Amoy 测试网 RPC URL (支持 WebSocket 或 HTTP)  
AMOY_RPC="wss://polygon-amoy.g.alchemy.com/v2/your-api-key"
# 或者使用 HTTP: AMOY_RPC="https://polygon-amoy.g.alchemy.com/v2/your-api-key"

# 跨链桥合约地址
# BNB 测试网上的桥合约地址
BNB_BRIDGE="0x0BD85507B2065b1c53859feEa4429621a57b03b0"

# Amoy 测试网上的桥合约地址
AMOY_BRIDGE="0x0c0a6367b3252c1a17437ed84f4c23b315e94f7c"

# 代币合约地址
# BNB 测试网上的测试代币地址
BNB_TOKEN="0xf7d2fF5F6F1403dCe33b63ae5028F822F70Ca34e"

# Amoy 测试网上的测试代币地址
AMOY_TOKEN="0x6d6c2a212537ff45847fdd7e02908f618484f51e"

# 私钥配置
# 用于签署跨链交易的私钥 (不包含 0x 前缀)
# 警告: 仅使用测试私钥，不要使用真实资金的私钥
PK="your_private_key_here_without_0x_prefix"

# Redis 配置 (可选)
# 如果需要自定义 Redis 配置，可以添加以下变量:
# REDIS_HOST="127.0.0.1"
# REDIS_PORT="6379"
# REDIS_PASSWORD=""