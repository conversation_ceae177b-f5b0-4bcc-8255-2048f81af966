{"name": "indexer", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "test-tx": "tsx src/testTransaction.ts", "dev": "tsx --watch src/index.ts", "build": "tsc"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@prisma/client": "^6.8.2", "chainsauce": "github:boudra/chainsauce#main", "cors": "^2.8.5", "dotenv": "^16.4.5", "ethers": "^6.13.4", "express": "^4.18.2", "tsx": "^4.19.2"}, "devDependencies": {"@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/node": "^20.0.0", "prisma": "^6.8.0", "typescript": "^5.0.0"}}