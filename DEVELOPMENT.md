# 🚀 开发环境使用指南

## 📋 概述

这个项目提供了两种 Docker Compose 配置：

- **`docker-compose.yml`**: 生产环境，使用预构建镜像
- **`docker-compose.dev.yml`**: 开发环境，支持本地编译和热重载

## 🛠️ 开发环境使用

### 1. **环境准备**

创建必要的环境配置文件：

```bash
# 创建 indexer 环境变量文件
cp indexer/.env.example indexer/.env

# 创建 frontend 环境变量文件 (如果需要)
touch frontend/.env
```

### 2. **启动开发环境**

```bash
# 启动开发环境
docker-compose -f docker-compose.dev.yml up -d

# 或者构建并启动
docker-compose -f docker-compose.dev.yml up --build -d
```

### 3. **查看日志**

```bash
# 查看所有服务日志
docker-compose -f docker-compose.dev.yml logs -f

# 查看特定服务日志
docker-compose -f docker-compose.dev.yml logs -f indexer
docker-compose -f docker-compose.dev.yml logs -f web
```

### 4. **停止服务**

```bash
# 停止所有服务
docker-compose -f docker-compose.dev.yml down

# 停止并删除 volumes (注意：会删除数据库数据)
docker-compose -f docker-compose.dev.yml down -v
```

## 🔥 热重载功能

### **Indexer (后端)**
- ✅ **源码热重载**: 修改 `indexer/src/` 下的 TypeScript 文件会自动重启服务
- ✅ **Prisma 热更新**: 修改 `indexer/prisma/` 下的 schema 文件后需要手动重启
- ✅ **环境变量**: 修改 `indexer/.env` 后需要重启容器

### **Frontend (前端)**
- ✅ **Vite 热重载**: 修改 `frontend/src/` 下的文件会自动热更新
- ✅ **配置热更新**: 修改 `vite.config.ts` 后会自动重启 Vite 服务
- ✅ **环境变量**: 修改 `frontend/.env` 后需要刷新页面

## 🌐 服务访问

| 服务 | 地址 | 说明 |
|-----|------|------|
| **Web 前端** | http://localhost:3000 | Vite 开发服务器 |
| **PostgreSQL** | localhost:5432 | 数据库连接 |
| **Redis** | localhost:6379 | 队列服务 |

## 🐛 调试技巧

### **进入容器调试**

```bash
# 进入 indexer 容器
docker-compose -f docker-compose.dev.yml exec indexer sh

# 进入 frontend 容器
docker-compose -f docker-compose.dev.yml exec web sh

# 进入数据库容器
docker-compose -f docker-compose.dev.yml exec postgres psql -U bridge_user -d bridge_db
```

### **重建特定服务**

```bash
# 重建 indexer 服务
docker-compose -f docker-compose.dev.yml up --build indexer

# 重建 frontend 服务
docker-compose -f docker-compose.dev.yml up --build web
```

### **手动运行命令**

```bash
# 在 indexer 容器中运行 Prisma 命令
docker-compose -f docker-compose.dev.yml exec indexer npx prisma generate
docker-compose -f docker-compose.dev.yml exec indexer npx prisma db push

# 在 frontend 容器中运行构建
docker-compose -f docker-compose.dev.yml exec web yarn build
```

## 📦 Volume 说明

开发环境使用了以下 volumes：

- **源码映射**: 只读映射源码目录，支持热重载
- **node_modules volumes**: 独立的 node_modules 避免宿主机和容器的依赖冲突
- **数据库数据**: 持久化存储开发数据

## ⚠️ 注意事项

1. **首次启动**: 第一次启动可能需要较长时间来安装依赖和构建
2. **端口冲突**: 确保本地没有其他服务占用 3000、5432、6379 端口
3. **磁盘空间**: 开发环境会创建较大的镜像和 volumes
4. **权限问题**: 如果遇到权限问题，检查 Docker 设置和目录权限

## 🔄 常用工作流

### **日常开发**

```bash
# 1. 启动开发环境
docker-compose -f docker-compose.dev.yml up -d

# 2. 编辑代码 (自动热重载)
# - 修改 indexer/src/ 下的文件
# - 修改 frontend/src/ 下的文件

# 3. 查看日志
docker-compose -f docker-compose.dev.yml logs -f

# 4. 停止环境
docker-compose -f docker-compose.dev.yml down
```

### **数据库变更**

```bash
# 1. 修改 prisma/schema.prisma
# 2. 推送到数据库
docker-compose -f docker-compose.dev.yml exec indexer npx prisma db push

# 3. 重新生成客户端 (自动会在容器启动时执行)
docker-compose -f docker-compose.dev.yml exec indexer npx prisma generate
```

## 🚀 生产环境部署

开发完成后，使用生产环境配置：

```bash
# 构建生产镜像
docker-compose build

# 启动生产环境
docker-compose up -d
``` 