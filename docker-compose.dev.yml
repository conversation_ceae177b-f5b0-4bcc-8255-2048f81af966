version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: bridge-postgres-dev
    restart: unless-stopped
    environment:
      POSTGRES_USER: bridge_user
      POSTGRES_PASSWORD: bridge_password
      POSTGRES_DB: bridge_db
    ports:
      - "5432:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    networks:
      - bridge-dev-network

  # Indexer 服务 - 本地构建
  indexer:
    build:
      context: ./indexer
      dockerfile: Dockerfile.dev
      target: development
    container_name: bridge-indexer-dev
    restart: unless-stopped
    ports:
      - "3001:3001"
    depends_on:
      - postgres
    environment:
      - NODE_ENV=development
      - DATABASE_URL=******************************************************/bridge_db
      
    volumes:
      - ./indexer/src:/app/src:ro
      - ./indexer/prisma:/app/prisma:ro
      - ./indexer/.env:/app/.env:ro
      - indexer_node_modules:/app/node_modules
    networks:
      - bridge-dev-network

  # Web 前端服务 - 本地构建
  web:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
      target: development
    container_name: bridge-web-dev
    restart: unless-stopped
    ports:
      - "5173:5173"
      - "24678:24678"  # Vite HMR port
    environment:
      - NODE_ENV=development
    volumes:
      - ./frontend/src:/app/src:ro
      - ./frontend/public:/app/public:ro
      - ./frontend/.env:/app/.env:ro
      - ./frontend/vite.config.ts:/app/vite.config.ts:ro
      - ./frontend/package.json:/app/package.json:ro
      - frontend_node_modules:/app/node_modules
    networks:
      - bridge-dev-network

networks:
  bridge-dev-network:
    driver: bridge

volumes:
  postgres_dev_data:
    driver: local
  indexer_node_modules:
    driver: local
  frontend_node_modules:
    driver: local 